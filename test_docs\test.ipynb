{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Test Jupyter Notebook\n", "\n", "This is a test notebook with code and markdown cells."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Python code cell\n", "def <PERSON><PERSON><PERSON><PERSON>(n):\n", "    if n <= 1:\n", "        return n\n", "    return fibon<PERSON>ci(n-1) + <PERSON><PERSON><PERSON><PERSON>(n-2)\n", "\n", "print(<PERSON><PERSON><PERSON><PERSON>(10))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Analysis Example\n", "\n", "Let's do some data analysis:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "# Create sample data\n", "data = pd.DataFrame({\n", "    'x': np.random.randn(100),\n", "    'y': np.random.randn(100)\n", "})\n", "\n", "print(data.head())"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}