"""
Code snippet extraction functionality.
"""

import logging
import re
from typing import List, Optional, Dict, Any
from abc import ABC, abstractmethod
from dataclasses import dataclass

from ..core.models import CodeSnippet, ParsedContent, ProcessingConfig


@dataclass
class ExtractionResult:
    """Result of code extraction operation."""
    
    snippets: List[CodeSnippet]
    total_extracted: int
    languages_detected: Dict[str, int]
    errors: List[str]


class CodeExtractor(ABC):
    """Abstract base class for code extraction."""
    
    @abstractmethod
    def extract(self, content: ParsedContent, config: ProcessingConfig) -> ExtractionResult:
        """
        Extract code snippets from parsed content.
        
        Args:
            content: Parsed content to extract from
            config: Processing configuration
            
        Returns:
            ExtractionResult with extracted snippets
        """
        pass


class RegexCodeExtractor(CodeExtractor):
    """
    Regex-based code extractor for fenced code blocks and inline code.
    """
    
    def __init__(self):
        """Initialize the extractor."""
        self.logger = logging.getLogger(__name__)
        
        # Enhanced regex patterns for different code block types
        self.fenced_block_pattern = re.compile(
            r'```(\w+)?\s*\n(.*?)\n```',
            re.DOTALL | re.MULTILINE
        )

        # Alternative fenced block patterns (tildes, different lengths)
        self.alt_fenced_pattern = re.compile(
            r'~~~(\w+)?\s*\n(.*?)\n~~~',
            re.DOTALL | re.MULTILINE
        )

        # HTML code patterns with various attributes
        self.html_code_pattern = re.compile(
            r'<(?:pre|code)(?:\s+(?:class="(?:language-)?(\w+)"|data-lang="(\w+)"))?[^>]*>(.*?)</(?:pre|code)>',
            re.DOTALL | re.IGNORECASE
        )

        # Script tags (JavaScript)
        self.script_pattern = re.compile(
            r'<script(?:\s+[^>]*)?>(?:\s*//.*?\n)?(.*?)</script>',
            re.DOTALL | re.IGNORECASE
        )

        # Style tags (CSS)
        self.style_pattern = re.compile(
            r'<style(?:\s+[^>]*)?>(?:\s*/\*.*?\*/\s*)?(.*?)</style>',
            re.DOTALL | re.IGNORECASE
        )

        # Inline code (more restrictive to avoid false positives)
        self.inline_code_pattern = re.compile(
            r'`([^`\n]{1,100})`'
        )

        # RST code blocks
        self.rst_code_pattern = re.compile(
            r'(?:^|\n)\s*\.\.\s+code(?:-block)?::\s*(\w+)?\s*\n((?:\n|\s{4,}.*)*)',
            re.MULTILINE
        )

        # RST literal blocks
        self.rst_literal_pattern = re.compile(
            r'(?:^|\n)(.*)::\s*\n((?:\n|\s{4,}.*)*)',
            re.MULTILINE
        )

        # AsciiDoc source blocks
        self.asciidoc_source_pattern = re.compile(
            r'\[source,(\w+)\]\s*\n-{4,}\s*\n(.*?)\n-{4,}',
            re.DOTALL | re.MULTILINE
        )

        # AsciiDoc listing blocks
        self.asciidoc_listing_pattern = re.compile(
            r'\.{4,}\s*\n(.*?)\n\.{4,}',
            re.DOTALL | re.MULTILINE
        )
        
        # Enhanced language detection heuristics
        self.language_heuristics = {
            'python': [
                r'def\s+\w+\(', r'import\s+\w+', r'from\s+\w+\s+import', r'print\(',
                r'if\s+__name__\s*==\s*["\']__main__["\']', r'class\s+\w+\(', r'self\.',
                r'@\w+', r'lambda\s+', r'yield\s+', r'with\s+\w+', r'except\s+\w*:',
                r'elif\s+', r'raise\s+\w+', r'assert\s+', r'pass\s*$'
            ],
            'javascript': [
                r'function\s+\w+\(', r'const\s+\w+\s*=', r'console\.log\(', r'=>',
                r'var\s+\w+\s*=', r'let\s+\w+\s*=', r'document\.', r'window\.',
                r'addEventListener\(', r'querySelector\(', r'async\s+function',
                r'await\s+', r'Promise\.', r'JSON\.', r'Array\.', r'Object\.'
            ],
            'typescript': [
                r'interface\s+\w+', r'type\s+\w+\s*=', r':\s*\w+\s*=', r'<\w+>',
                r'export\s+interface', r'export\s+type', r'implements\s+\w+',
                r'extends\s+\w+', r'public\s+\w+', r'private\s+\w+', r'readonly\s+'
            ],
            'java': [
                r'public\s+class\s+\w+', r'public\s+static\s+void\s+main', r'System\.out\.println',
                r'import\s+java\.', r'@Override', r'extends\s+\w+', r'implements\s+\w+',
                r'private\s+\w+', r'protected\s+\w+', r'final\s+\w+', r'static\s+\w+'
            ],
            'csharp': [
                r'using\s+System', r'namespace\s+\w+', r'public\s+class\s+\w+',
                r'Console\.WriteLine', r'static\s+void\s+Main', r'var\s+\w+\s*=',
                r'public\s+static\s+', r'private\s+\w+', r'protected\s+\w+', r'override\s+'
            ],
            'cpp': [
                r'#include\s*<', r'using\s+namespace', r'int\s+main\(', r'std::',
                r'cout\s*<<', r'cin\s*>>', r'class\s+\w+', r'public:', r'private:',
                r'virtual\s+', r'const\s+\w+', r'template\s*<'
            ],
            'c': [
                r'#include\s*<', r'int\s+main\(', r'printf\(', r'scanf\(',
                r'malloc\(', r'free\(', r'struct\s+\w+', r'typedef\s+',
                r'static\s+\w+', r'extern\s+\w+'
            ],
            'go': [
                r'package\s+\w+', r'import\s+\(', r'func\s+\w+\(', r'fmt\.Print',
                r'var\s+\w+\s+\w+', r'type\s+\w+\s+struct', r'interface\s*{',
                r'go\s+\w+\(', r'defer\s+', r'chan\s+\w+'
            ],
            'rust': [
                r'fn\s+\w+\(', r'let\s+\w+\s*=', r'use\s+\w+', r'struct\s+\w+',
                r'impl\s+\w+', r'trait\s+\w+', r'enum\s+\w+', r'match\s+\w+',
                r'println!\(', r'vec!\[', r'&mut\s+', r'pub\s+fn'
            ],
            'sql': [
                r'SELECT\s+', r'FROM\s+\w+', r'WHERE\s+', r'INSERT\s+INTO',
                r'UPDATE\s+\w+', r'DELETE\s+FROM', r'CREATE\s+TABLE', r'ALTER\s+TABLE',
                r'JOIN\s+\w+', r'GROUP\s+BY', r'ORDER\s+BY', r'HAVING\s+'
            ],
            'bash': [
                r'#!/bin/bash', r'\$\w+', r'echo\s+', r'if\s*\[', r'for\s+\w+\s+in',
                r'while\s*\[', r'function\s+\w+', r'export\s+\w+', r'source\s+',
                r'chmod\s+', r'grep\s+', r'awk\s+', r'sed\s+'
            ],
            'powershell': [
                r'\$\w+\s*=', r'Get-\w+', r'Set-\w+', r'New-\w+', r'Remove-\w+',
                r'Write-Host', r'Write-Output', r'param\(', r'function\s+\w+',
                r'if\s*\(', r'foreach\s*\(', r'while\s*\('
            ],
            'html': [
                r'<!DOCTYPE\s+html>', r'<html>', r'<head>', r'<body>', r'<div',
                r'<p>', r'<script>', r'<style>', r'<link\s+', r'<meta\s+',
                r'class\s*=', r'id\s*=', r'href\s*='
            ],
            'css': [
                r'\.\w+\s*{', r'#\w+\s*{', r':\s*\w+;', r'@media\s+',
                r'@import\s+', r'@keyframes\s+', r'hover\s*:', r'focus\s*:',
                r'margin\s*:', r'padding\s*:', r'color\s*:', r'background\s*:'
            ],
            'json': [
                r'^\s*{', r'^\s*\[', r'"\w+"\s*:', r':\s*"', r':\s*\d+',
                r':\s*true', r':\s*false', r':\s*null'
            ],
            'yaml': [
                r'^\w+:', r'^\s+-\s+', r'---\s*$', r'^\s*#', r':\s*\|',
                r':\s*>', r'version\s*:', r'name\s*:', r'description\s*:'
            ],
            'xml': [
                r'<\?xml\s+', r'<\w+>', r'</\w+>', r'xmlns\s*=', r'<!\[CDATA\[',
                r'<!--', r'-->', r'<\w+\s+\w+\s*='
            ],
            'dockerfile': [
                r'^FROM\s+', r'^RUN\s+', r'^COPY\s+', r'^ADD\s+', r'^WORKDIR\s+',
                r'^EXPOSE\s+', r'^ENV\s+', r'^CMD\s+', r'^ENTRYPOINT\s+'
            ],
            'makefile': [
                r'^\w+:', r'\$\(\w+\)', r'\.PHONY\s*:', r'include\s+',
                r'ifdef\s+', r'ifndef\s+', r'endif\s*$', r'@echo\s+'
            ]
        }
    
    def extract(self, content: ParsedContent, config: ProcessingConfig) -> ExtractionResult:
        """
        Extract code snippets using regex patterns.
        
        Args:
            content: Parsed content with raw text
            config: Processing configuration with language filters
            
        Returns:
            ExtractionResult with extracted code snippets
        """
        self.logger.debug(f"Extracting code from {content.file_path}")
        
        snippets = []
        languages_detected = {}
        errors = []
        
        if content.error or not content.raw_content:
            return ExtractionResult(
                snippets=[],
                total_extracted=0,
                languages_detected={},
                errors=[content.error] if content.error else ["No content to extract from"]
            )
        
        try:
            # Extract fenced code blocks (standard and alternative)
            fenced_snippets = self._extract_fenced_blocks(content.raw_content, content.file_path)
            snippets.extend(fenced_snippets)

            alt_fenced_snippets = self._extract_alt_fenced_blocks(content.raw_content, content.file_path)
            snippets.extend(alt_fenced_snippets)

            # Extract HTML code blocks
            html_snippets = self._extract_html_blocks(content.raw_content, content.file_path)
            snippets.extend(html_snippets)

            # Extract script and style tags
            script_snippets = self._extract_script_blocks(content.raw_content, content.file_path)
            snippets.extend(script_snippets)

            style_snippets = self._extract_style_blocks(content.raw_content, content.file_path)
            snippets.extend(style_snippets)

            # Extract format-specific blocks based on file type
            if content.file_type == "rst":
                rst_snippets = self._extract_rst_blocks(content.raw_content, content.file_path)
                snippets.extend(rst_snippets)
            elif content.file_type == "asciidoc":
                asciidoc_snippets = self._extract_asciidoc_blocks(content.raw_content, content.file_path)
                snippets.extend(asciidoc_snippets)

            # Extract inline code if configured
            if config.mode == "full_context":
                inline_snippets = self._extract_inline_code(content.raw_content, content.file_path)
                snippets.extend(inline_snippets)
            
            # Filter by language if specified
            if config.languages:
                snippets = self._filter_by_language(snippets, config.languages)
            
            # Count languages
            for snippet in snippets:
                lang = snippet.language or "unknown"
                languages_detected[lang] = languages_detected.get(lang, 0) + 1
            
        except Exception as e:
            error_msg = f"Extraction error for {content.file_path}: {e}"
            self.logger.error(error_msg)
            errors.append(error_msg)
        
        result = ExtractionResult(
            snippets=snippets,
            total_extracted=len(snippets),
            languages_detected=languages_detected,
            errors=errors
        )
        
        self.logger.debug(f"Extracted {len(snippets)} snippets from {content.file_path}")
        return result
    
    def _extract_fenced_blocks(self, text: str, file_path) -> List[CodeSnippet]:
        """Extract fenced code blocks (```language)."""
        snippets = []
        
        for match in self.fenced_block_pattern.finditer(text):
            language = match.group(1)
            code_content = match.group(2).strip()
            
            if not code_content:
                continue
            
            # Detect language if not specified
            if not language:
                language = self._detect_language(code_content)
            
            snippet = CodeSnippet(
                content=code_content,
                language=language,
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )
            
            snippets.append(snippet)
        
        return snippets
    
    def _extract_html_blocks(self, text: str, file_path) -> List[CodeSnippet]:
        """Extract HTML code blocks (<pre><code>)."""
        snippets = []
        
        for match in self.html_code_pattern.finditer(text):
            # Handle multiple capture groups for language detection
            language = match.group(1) or match.group(2)  # class="language-X" or data-lang="X"
            code_content = match.group(3).strip()
            
            if not code_content:
                continue
            
            # Clean HTML entities and tags
            code_content = self._clean_html_content(code_content)
            
            # Detect language if not specified
            if not language:
                language = self._detect_language(code_content)
            
            snippet = CodeSnippet(
                content=code_content,
                language=language,
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )
            
            snippets.append(snippet)
        
        return snippets
    
    def _extract_inline_code(self, text: str, file_path) -> List[CodeSnippet]:
        """Extract inline code (`code`)."""
        snippets = []
        
        for match in self.inline_code_pattern.finditer(text):
            code_content = match.group(1).strip()
            
            if not code_content or len(code_content) < 3:  # Skip very short inline code
                continue
            
            snippet = CodeSnippet(
                content=code_content,
                language="inline",
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )
            
            snippets.append(snippet)
        
        return snippets
    
    def _detect_language(self, code: str) -> Optional[str]:
        """
        Detect programming language using enhanced heuristics.

        Args:
            code: Code content to analyze

        Returns:
            Detected language name or None
        """
        if not code.strip():
            return None

        # Score each language based on pattern matches
        scores = {}
        code_lines = code.split('\n')

        for language, patterns in self.language_heuristics.items():
            score = 0
            pattern_matches = 0

            for pattern in patterns:
                # Check entire code block
                if re.search(pattern, code, re.IGNORECASE | re.MULTILINE):
                    pattern_matches += 1

                    # Give extra weight to patterns found in first few lines
                    for i, line in enumerate(code_lines[:5]):
                        if re.search(pattern, line, re.IGNORECASE):
                            score += 2  # Higher weight for early matches
                            break
                    else:
                        score += 1  # Standard weight for other matches

            # Bonus for multiple pattern matches (indicates stronger confidence)
            if pattern_matches > 1:
                score += pattern_matches * 0.5

            if score > 0:
                scores[language] = score

        # Return language with highest score, but only if confident enough
        if scores:
            best_language = max(scores, key=scores.get)
            best_score = scores[best_language]

            # Require minimum confidence threshold
            if best_score >= 2.0:
                return best_language

        # Fallback: try to detect by common file extensions in comments
        extension_hints = {
            '.py': 'python', '.js': 'javascript', '.ts': 'typescript',
            '.java': 'java', '.cs': 'csharp', '.cpp': 'cpp', '.c': 'c',
            '.go': 'go', '.rs': 'rust', '.sql': 'sql', '.sh': 'bash',
            '.ps1': 'powershell', '.html': 'html', '.css': 'css',
            '.json': 'json', '.yml': 'yaml', '.yaml': 'yaml', '.xml': 'xml'
        }

        for ext, lang in extension_hints.items():
            if ext in code.lower():
                return lang

        return None
    
    def _clean_html_content(self, content: str) -> str:
        """Clean HTML entities and tags from code content."""
        # Basic HTML entity decoding
        content = content.replace('&lt;', '<')
        content = content.replace('&gt;', '>')
        content = content.replace('&amp;', '&')
        content = content.replace('&quot;', '"')
        content = content.replace('&#39;', "'")
        
        # Remove HTML tags
        content = re.sub(r'<[^>]+>', '', content)
        
        return content
    
    def _filter_by_language(self, snippets: List[CodeSnippet], allowed_languages: List[str]) -> List[CodeSnippet]:
        """Filter snippets by allowed languages."""
        allowed_set = set(lang.lower() for lang in allowed_languages)
        
        filtered = []
        for snippet in snippets:
            if snippet.language and snippet.language.lower() in allowed_set:
                filtered.append(snippet)
            elif not snippet.language and "unknown" in allowed_set:
                filtered.append(snippet)
        
        return filtered

    def _extract_alt_fenced_blocks(self, text: str, file_path) -> List[CodeSnippet]:
        """Extract alternative fenced code blocks (~~~language)."""
        snippets = []

        for match in self.alt_fenced_pattern.finditer(text):
            language = match.group(1)
            code_content = match.group(2).strip()

            if not code_content:
                continue

            # Detect language if not specified
            if not language:
                language = self._detect_language(code_content)

            snippet = CodeSnippet(
                content=code_content,
                language=language,
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )

            snippets.append(snippet)

        return snippets

    def _extract_script_blocks(self, text: str, file_path) -> List[CodeSnippet]:
        """Extract JavaScript from <script> tags."""
        snippets = []

        for match in self.script_pattern.finditer(text):
            code_content = match.group(1).strip()

            if not code_content:
                continue

            # Clean HTML entities
            code_content = self._clean_html_content(code_content)

            snippet = CodeSnippet(
                content=code_content,
                language="javascript",
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )

            snippets.append(snippet)

        return snippets

    def _extract_style_blocks(self, text: str, file_path) -> List[CodeSnippet]:
        """Extract CSS from <style> tags."""
        snippets = []

        for match in self.style_pattern.finditer(text):
            code_content = match.group(1).strip()

            if not code_content:
                continue

            # Clean HTML entities
            code_content = self._clean_html_content(code_content)

            snippet = CodeSnippet(
                content=code_content,
                language="css",
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )

            snippets.append(snippet)

        return snippets

    def _extract_rst_blocks(self, text: str, file_path) -> List[CodeSnippet]:
        """Extract RST code blocks and literal blocks."""
        snippets = []

        # Extract code-block:: and code:: directives
        for match in self.rst_code_pattern.finditer(text):
            language = match.group(1)
            code_content = match.group(2)

            if not code_content:
                continue

            # Clean indentation (RST uses 4+ spaces for code blocks)
            lines = code_content.split('\n')
            cleaned_lines = []
            for line in lines:
                if line.strip():  # Non-empty line
                    if line.startswith('    '):
                        cleaned_lines.append(line[4:])  # Remove 4-space indent
                    else:
                        cleaned_lines.append(line.lstrip())
                else:
                    cleaned_lines.append('')

            code_content = '\n'.join(cleaned_lines).strip()

            if not code_content:
                continue

            # Detect language if not specified
            if not language:
                language = self._detect_language(code_content)

            snippet = CodeSnippet(
                content=code_content,
                language=language,
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )

            snippets.append(snippet)

        # Extract literal blocks (::)
        for match in self.rst_literal_pattern.finditer(text):
            intro_text = match.group(1).strip()
            code_content = match.group(2)

            if not code_content or 'code' not in intro_text.lower():
                continue

            # Clean indentation
            lines = code_content.split('\n')
            cleaned_lines = []
            for line in lines:
                if line.strip():
                    if line.startswith('    '):
                        cleaned_lines.append(line[4:])
                    else:
                        cleaned_lines.append(line.lstrip())
                else:
                    cleaned_lines.append('')

            code_content = '\n'.join(cleaned_lines).strip()

            if not code_content:
                continue

            language = self._detect_language(code_content)

            snippet = CodeSnippet(
                content=code_content,
                language=language,
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )

            snippets.append(snippet)

        return snippets

    def _extract_asciidoc_blocks(self, text: str, file_path) -> List[CodeSnippet]:
        """Extract AsciiDoc source blocks and listing blocks."""
        snippets = []

        # Extract [source,language] blocks
        for match in self.asciidoc_source_pattern.finditer(text):
            language = match.group(1)
            code_content = match.group(2).strip()

            if not code_content:
                continue

            snippet = CodeSnippet(
                content=code_content,
                language=language,
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )

            snippets.append(snippet)

        # Extract listing blocks (.... blocks)
        for match in self.asciidoc_listing_pattern.finditer(text):
            code_content = match.group(1).strip()

            if not code_content:
                continue

            language = self._detect_language(code_content)

            snippet = CodeSnippet(
                content=code_content,
                language=language,
                file_path=file_path,
                start_line=text[:match.start()].count('\n') + 1,
                end_line=text[:match.end()].count('\n') + 1
            )

            snippets.append(snippet)

        return snippets
