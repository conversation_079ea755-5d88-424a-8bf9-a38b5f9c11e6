"""
Configuration management for generate-llms.
"""

import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field, field_validator, ValidationError

from .models import ProcessingConfig


class ConfigSchema(BaseModel):
    """Pydantic schema for configuration validation."""

    input_directory: Optional[str] = None
    output_file: Optional[str] = None
    mode: str = Field(default="full_context", pattern="^(full_context|snippets_only)$")
    max_depth: Optional[int] = Field(default=None, ge=1)
    languages: Optional[list] = None
    exclude_patterns: Optional[list] = None
    include_folders: Optional[list] = None

    # Project metadata (Context7-inspired)
    project_title: Optional[str] = None
    project_description: Optional[str] = None
    project_rules: Optional[list] = None

    # LLM settings
    llm: Optional[Dict[str, Any]] = Field(default_factory=dict)

    # Logging settings
    logging: Optional[Dict[str, Any]] = Field(default_factory=dict)

    # Performance settings
    performance: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    @field_validator('languages')
    @classmethod
    def validate_languages(cls, v):
        if v is not None and not isinstance(v, list):
            raise ValueError('languages must be a list')
        return v

    @field_validator('exclude_patterns')
    @classmethod
    def validate_exclude_patterns(cls, v):
        if v is not None and not isinstance(v, list):
            raise ValueError('exclude_patterns must be a list')
        return v

    @field_validator('llm')
    @classmethod
    def validate_llm_config(cls, v):
        if v is not None:
            # Validate LLM backend
            if 'backend' in v and v['backend'] not in ['ollama', 'llamacpp', 'transformers']:
                raise ValueError(f"Invalid LLM backend: {v['backend']}. Must be one of: ollama, llamacpp, transformers")

            # Validate temperature
            if 'temperature' in v:
                temp = v['temperature']
                if not isinstance(temp, (int, float)) or not (0.1 <= temp <= 1.0):
                    raise ValueError(f"LLM temperature must be between 0.1 and 1.0, got: {temp}")

            # Validate max_tokens
            if 'max_tokens' in v:
                tokens = v['max_tokens']
                if not isinstance(tokens, int) or not (50 <= tokens <= 2000):
                    raise ValueError(f"LLM max_tokens must be between 50 and 2000, got: {tokens}")

        return v

    @field_validator('performance')
    @classmethod
    def validate_performance_config(cls, v):
        if v is not None:
            # Validate parallel processing settings
            if 'max_workers' in v:
                workers = v['max_workers']
                if not isinstance(workers, int) or workers < 1:
                    raise ValueError(f"max_workers must be a positive integer, got: {workers}")

            # Validate memory limits
            if 'memory_limit_mb' in v:
                limit = v['memory_limit_mb']
                if not isinstance(limit, int) or limit < 100:
                    raise ValueError(f"memory_limit_mb must be at least 100, got: {limit}")

        return v


def load_config(config_file: Path) -> Dict[str, Any]:
    """
    Load and validate configuration from YAML file.
    
    Args:
        config_file: Path to YAML configuration file
        
    Returns:
        Validated configuration dictionary
        
    Raises:
        FileNotFoundError: If config file doesn't exist
        yaml.YAMLError: If YAML is invalid
        ValueError: If configuration is invalid
    """
    if not config_file.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_file}")
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            raw_config = yaml.safe_load(f)
    except yaml.YAMLError as e:
        raise yaml.YAMLError(f"Invalid YAML in configuration file: {e}")
    
    if raw_config is None:
        raw_config = {}
    
    # Validate configuration
    try:
        config_schema = ConfigSchema(**raw_config)
        return config_schema.model_dump(exclude_none=True)
    except Exception as e:
        raise ValueError(f"Invalid configuration: {e}")


def merge_config_with_cli(
    config_dict: Dict[str, Any], 
    cli_config: ProcessingConfig
) -> ProcessingConfig:
    """
    Merge configuration file with CLI arguments.
    CLI arguments take precedence over config file.
    
    Args:
        config_dict: Configuration from file
        cli_config: Configuration from CLI arguments
        
    Returns:
        Merged ProcessingConfig
    """
    # Start with CLI config as base (highest precedence)
    merged_config = cli_config
    
    # Override with config file values only if CLI didn't specify them
    if 'input_directory' in config_dict and cli_config.input_directory == Path('.'):
        merged_config.input_directory = Path(config_dict['input_directory'])
    
    if 'output_file' in config_dict and cli_config.output_file == Path('./llms.txt'):
        merged_config.output_file = Path(config_dict['output_file'])
    
    if 'mode' in config_dict and cli_config.mode == "full_context":
        merged_config.mode = config_dict['mode']
    
    if 'max_depth' in config_dict and cli_config.max_depth is None:
        merged_config.max_depth = config_dict['max_depth']
    
    if 'languages' in config_dict and cli_config.languages is None:
        merged_config.languages = config_dict['languages']
    
    if 'exclude_patterns' in config_dict and cli_config.exclude_patterns is None:
        merged_config.exclude_patterns = config_dict['exclude_patterns']

    if 'include_folders' in config_dict and cli_config.include_folders is None:
        merged_config.include_folders = config_dict['include_folders']

    # Handle project metadata
    if 'project_title' in config_dict and cli_config.project_title is None:
        merged_config.project_title = config_dict['project_title']

    if 'project_description' in config_dict and cli_config.project_description is None:
        merged_config.project_description = config_dict['project_description']

    if 'project_rules' in config_dict and cli_config.project_rules is None:
        merged_config.project_rules = config_dict['project_rules']
    
    # Handle LLM configuration
    if 'llm' in config_dict:
        llm_config = config_dict['llm']
        
        if 'enabled' in llm_config and not cli_config.llm_rewrite_enabled:
            merged_config.llm_rewrite_enabled = llm_config['enabled']
        
        if 'backend' in llm_config and cli_config.llm_backend == "ollama":
            merged_config.llm_backend = llm_config['backend']
        
        if 'model' in llm_config and cli_config.llm_model == "llama2:7b":
            merged_config.llm_model = llm_config['model']
        
        if 'temperature' in llm_config:
            merged_config.llm_temperature = llm_config['temperature']
        
        if 'max_tokens' in llm_config:
            merged_config.llm_max_tokens = llm_config['max_tokens']
    
    # Handle logging configuration
    if 'logging' in config_dict:
        logging_config = config_dict['logging']
        
        if 'level' in logging_config and not cli_config.verbose:
            merged_config.verbose = logging_config['level'].upper() in ['DEBUG', 'INFO']
    
    return merged_config
