[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "generate-llms"
version = "1.0.0"
description = "CLI tool that recursively scans local directories for documentation files, extracts code snippets with contextual information, and generates a consolidated llms.txt file optimized for LLM consumption."
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "The Augster", email = "<EMAIL>"},
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Documentation",
    "Topic :: Software Development :: Documentation",
    "Topic :: Text Processing :: Markup",
]
requires-python = ">=3.8"
dependencies = [
    "typer>=0.9.0",
    "charset-normalizer>=3.0.0",
    "markdown>=3.5.0",
    "beautifulsoup4>=4.12.0",
    "nbformat>=5.9.0",
    "docutils>=0.19.0",
    "PyYAML>=6.0.0",
    "pydantic>=2.0.0",
    "requests>=2.31.0",
]

[project.optional-dependencies]
llm = [
    "llama-cpp-python>=0.2.0",
    "transformers>=4.35.0",
    "torch>=2.0.0",
]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "hypothesis>=6.88.0",
    "black>=23.9.0",
    "isort>=5.12.0",
    "mypy>=1.6.0",
    "pre-commit>=3.4.0",
]
docs = [
    "sphinx>=7.2.0",
    "sphinx-rtd-theme>=1.3.0",
    "myst-parser>=2.0.0",
]

[project.scripts]
generate-llms = "generate_llms.cli:main"

[project.urls]
Homepage = "https://github.com/augster/generate-llms"
Repository = "https://github.com/augster/generate-llms"
Issues = "https://github.com/augster/generate-llms/issues"
Documentation = "https://generate-llms.readthedocs.io"

[tool.hatch.build.targets.wheel]
packages = ["src/generate_llms"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["generate_llms"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=generate_llms",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src/generate_llms"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
