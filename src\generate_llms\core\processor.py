"""
Main document processor orchestrating the entire pipeline.
"""

import time
import logging
from pathlib import Path
from typing import List

from .models import ProcessingConfig, ProcessingResult, ParsedContent
from .config import load_config, merge_config_with_cli
from ..scanners import RecursiveFileScanner
from ..parsers import ContentParserFactory
from ..extractors import RegexCodeExtractor, ContextExtractor
from ..llm import LLMBackendFactory
from ..writers import OutputWriter, MarkdownOutputWriter, MetadataGenerator


class DocumentProcessor:
    """
    Main processor that orchestrates the entire document processing pipeline.
    """
    
    def __init__(self, config: ProcessingConfig):
        """
        Initialize the document processor.
        
        Args:
            config: Processing configuration
        """
        self.config = config
        self._setup_logging()
        
        # Load and merge configuration file if provided
        if config.config_file:
            try:
                file_config = load_config(config.config_file)
                self.config = merge_config_with_cli(file_config, config)
                logging.info(f"Loaded configuration from {config.config_file}")
            except Exception as e:
                logging.warning(f"Failed to load config file: {e}")
    
    def _setup_logging(self) -> None:
        """Setup logging configuration."""
        level = logging.DEBUG if self.config.verbose else logging.INFO
        logging.basicConfig(
            level=level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Suppress verbose logs from dependencies unless in debug mode
        if not self.config.verbose:
            logging.getLogger('charset_normalizer').setLevel(logging.WARNING)
            logging.getLogger('urllib3').setLevel(logging.WARNING)
    
    def process(self) -> ProcessingResult:
        """
        Process documents according to configuration.
        
        Returns:
            ProcessingResult with metrics and metadata
        """
        start_time = time.time()
        logging.info(f"Starting document processing: {self.config.input_directory}")
        
        # Initialize result
        result = ProcessingResult(
            total_files_scanned=0,
            files_with_snippets=0,
            excluded_files=0,
            total_snippets=0,
            scan_depth=self.config.max_depth,
        )
        
        try:
            # Step 1: Scan files (placeholder - will be implemented in next task)
            logging.info("Scanning files...")
            scanned_files = self._scan_files()
            result.total_files_scanned = len(scanned_files)
            
            # Step 2: Parse content (placeholder - will be implemented in next task)
            logging.info("Parsing content...")
            parsed_contents = self._parse_files(scanned_files)
            
            # Step 3: Extract code snippets (placeholder - will be implemented in next task)
            logging.info("Extracting code snippets...")
            self._extract_snippets(parsed_contents)
            
            # Step 4: Apply LLM enhancement if enabled
            if self.config.llm_rewrite_enabled or self.config.llm_backend:
                logging.info("Applying LLM enhancement...")
                self._enhance_content(parsed_contents)
            
            # Step 5: Generate output (placeholder - will be implemented in next task)
            if not self.config.dry_run:
                logging.info("Generating output...")
                self._generate_output(parsed_contents, result)
            
            # Calculate final metrics
            self._calculate_metrics(parsed_contents, result)
            
        except Exception as e:
            logging.error(f"Processing failed: {e}")
            result.errors.append(str(e))
            raise
        
        finally:
            result.processing_time_seconds = time.time() - start_time
            logging.info(f"Processing completed in {result.processing_time_seconds:.2f} seconds")
        
        return result
    
    def _scan_files(self) -> List[Path]:
        """Scan directory for supported files."""
        logging.info("Scanning files with RecursiveFileScanner")

        scanner = RecursiveFileScanner()
        scan_result = scanner.scan(self.config.input_directory, self.config)

        if scan_result.errors:
            for error in scan_result.errors:
                logging.error(f"Scan error: {error}")

        logging.info(f"Found {len(scan_result.files)} files, excluded {scan_result.excluded_count}")
        return scan_result.files
    
    def _parse_files(self, files: List[Path]) -> List[ParsedContent]:
        """Parse files into structured content."""
        logging.info(f"Parsing {len(files)} files with ContentParserFactory")

        parser_factory = ContentParserFactory()
        parsed_contents = []

        for file_path in files:
            try:
                parsed_content = parser_factory.parse_file(file_path, self.config)
                if parsed_content:
                    parsed_contents.append(parsed_content)
                    if parsed_content.error:
                        logging.warning(f"Parse error for {file_path}: {parsed_content.error}")
                else:
                    logging.warning(f"No parser available for {file_path}")
            except Exception as e:
                logging.error(f"Failed to parse {file_path}: {e}")

        logging.info(f"Successfully parsed {len(parsed_contents)} files")
        return parsed_contents
    
    def _extract_snippets(self, contents: List[ParsedContent]) -> None:
        """Extract code snippets from parsed content and add context."""
        logging.info(f"Extracting code snippets from {len(contents)} files")

        extractor = RegexCodeExtractor()
        context_extractor = ContextExtractor()
        total_snippets = 0

        for content in contents:
            if content.error:
                logging.warning(f"Skipping extraction for {content.file_path} due to parse error")
                continue

            try:
                # Extract code snippets
                extraction_result = extractor.extract(content, self.config)

                # Update the content with extracted snippets
                content.snippets = extraction_result.snippets
                total_snippets += extraction_result.total_extracted

                # Add context to snippets based on processing mode
                if content.snippets:
                    context_extractor.add_context_to_snippets(content, self.config)
                    logging.debug(f"Added context to {len(content.snippets)} snippets from {content.file_path}")

                if extraction_result.errors:
                    for error in extraction_result.errors:
                        logging.error(f"Extraction error: {error}")

                logging.debug(f"Extracted {extraction_result.total_extracted} snippets from {content.file_path}")

            except Exception as e:
                logging.error(f"Failed to extract snippets from {content.file_path}: {e}")

        logging.info(f"Total snippets extracted: {total_snippets}")
    
    def _enhance_content(self, contents: List[ParsedContent]) -> None:
        """Apply LLM enhancement to content."""
        if not self.config.llm_backend:
            logging.info("LLM enhancement disabled (no backend specified)")
            return

        logging.info(f"Enhancing content with {self.config.llm_backend} backend")

        # Create LLM backend
        factory = LLMBackendFactory()
        backend = factory.create_backend(self.config.llm_backend)

        if not backend:
            logging.warning(f"LLM backend '{self.config.llm_backend}' not available, skipping enhancement")
            return

        if not backend.is_available():
            logging.warning(f"LLM backend '{self.config.llm_backend}' not available, skipping enhancement")
            return

        total_snippets = 0
        enhanced_snippets = 0
        failed_enhancements = 0

        for content in contents:
            if not content.snippets:
                continue

            logging.debug(f"Enhancing {len(content.snippets)} snippets from {content.file_path}")

            for snippet in content.snippets:
                total_snippets += 1

                try:
                    # Build context for enhancement
                    context_parts = []
                    if snippet.heading:
                        context_parts.append(f"Section: {snippet.heading}")
                    if snippet.context_before:
                        context_parts.append(f"Context: {snippet.context_before}")

                    context = " | ".join(context_parts) if context_parts else "No additional context"

                    # Enhance the snippet
                    response = backend.enhance_content(
                        snippet.content,
                        context,
                        self.config
                    )

                    if response.success:
                        # Store original content and update with enhanced version
                        snippet.original_content = snippet.content
                        snippet.content = response.enhanced_content
                        enhanced_snippets += 1

                        logging.debug(f"Enhanced snippet from {content.file_path} (line {snippet.start_line})")
                    else:
                        failed_enhancements += 1
                        logging.warning(f"Failed to enhance snippet: {response.error}")

                except Exception as e:
                    failed_enhancements += 1
                    logging.error(f"Enhancement error for snippet in {content.file_path}: {e}")

        logging.info(f"Enhancement complete: {enhanced_snippets}/{total_snippets} snippets enhanced, {failed_enhancements} failed")
    
    def _generate_output(self, contents: List[ParsedContent], result: ProcessingResult) -> None:
        """Generate final output file."""
        logging.info(f"Generating output file: {self.config.output_file}")

        # Create output writer
        writer = MarkdownOutputWriter()

        try:
            # Generate output and get metrics
            output_metrics = writer.write(contents, result, self.config)

            # Update result with output metrics
            result.estimated_tokens = output_metrics.estimated_tokens
            result.estimated_reading_time_minutes = output_metrics.estimated_reading_time_minutes

            # Log output statistics
            logging.info(f"Output generated successfully:")
            logging.info(f"  - File: {self.config.output_file}")
            logging.info(f"  - Estimated tokens: {output_metrics.estimated_tokens:,}")
            logging.info(f"  - Estimated reading time: {output_metrics.estimated_reading_time_minutes} minutes")
            logging.info(f"  - Total snippets: {output_metrics.total_snippets}")

            # Log context window warnings if any
            if output_metrics.context_window_warnings:
                logging.warning("Context window warnings:")
                for warning in output_metrics.context_window_warnings:
                    logging.warning(f"  - {warning}")

        except Exception as e:
            error_msg = f"Failed to generate output: {e}"
            logging.error(error_msg)
            result.errors.append(error_msg)
            raise
    
    def _calculate_metrics(self, contents: List[ParsedContent], result: ProcessingResult) -> None:
        """Calculate quality metrics and statistics."""
        logging.info("Calculating quality metrics and statistics")

        try:
            # Create metadata generator
            generator = MetadataGenerator()

            # Calculate comprehensive metrics
            generator.calculate_metrics(contents, result, self.config)

            # Update result with file statistics
            result.files_with_snippets = sum(1 for content in contents if content.snippets)
            result.total_snippets = sum(len(content.snippets) for content in contents)

            # Log calculated metrics
            logging.info(f"Quality metrics calculated:")
            logging.info(f"  - Trust score: {result.trust_score:.1f}/10")
            logging.info(f"  - Completeness score: {result.completeness_score:.1f}/10")
            logging.info(f"  - Code coverage: {result.code_coverage_percentage:.1f}%")
            logging.info(f"  - Files with snippets: {result.files_with_snippets}/{result.total_files_scanned}")

        except Exception as e:
            error_msg = f"Failed to calculate metrics: {e}"
            logging.error(error_msg)
            result.errors.append(error_msg)
