# Generate LLMs - Local Documentation Code Snippet Aggregator

A CLI tool that recursively scans local directories for documentation files, extracts code snippets with contextual information, and generates a consolidated `llms.txt` file optimized for LLM consumption.

## Features

- **Multi-format support**: Markdown (.md, .mdx), HTML, reStructuredText (.rst), Jupyter notebooks (.ipynb), AsciiDoc (.adoc)
- **Intelligent code extraction**: Detects fenced code blocks, HTML code tags, and inline code with language identification
- **Context preservation**: Full context mode includes surrounding text and headings
- **Optional LLM enhancement**: Local LLM integration for content rewriting and improvement
- **Comprehensive metadata**: Detailed processing statistics and quality metrics
- **Flexible configuration**: CLI options and YAML configuration file support
- **Performance optimized**: Streaming processing and memory management for large directories

## Installation

```bash
# Install from source
git clone https://github.com/augster/generate-llms.git
cd generate-llms
pip install -e .

# Install with LLM support
pip install -e ".[llm]"

# Install development dependencies
pip install -e ".[dev]"
```

## Quick Start

```bash
# Basic usage
generate-llms ./docs

# Snippets only with custom output
generate-llms ./docs --mode snippets_only --output ./code_snippets.txt

# With LLM rewriting
generate-llms ./project --rewrite --llm-backend ollama

# Filter languages and exclude patterns
generate-llms ./api_docs --languages python,javascript --exclude-patterns "*.test.md,temp/*"
```

## CLI Options

```
Usage: generate-llms [OPTIONS] INPUT_DIRECTORY

Arguments:
  INPUT_DIRECTORY  Path to directory containing documentation files

Options:
  -o, --output PATH           Output file path (default: ./llms.txt)
  -m, --mode MODE            Extraction mode: full_context|snippets_only
  -d, --depth INT            Maximum scan depth (default: unlimited)
  -l, --languages LIST       Comma-separated list of languages to include
  --exclude-patterns LIST     Glob patterns for files/dirs to exclude
  --rewrite                   Enable LLM-based content rewriting
  --llm-backend BACKEND       LLM backend: ollama|llamacpp|transformers
  --llm-model MODEL          Model name for rewriting
  --config FILE              Path to configuration file
  -v, --verbose              Enable detailed logging
  --dry-run                  Show what would be processed without output
  --help                     Show this message and exit
```

## Configuration File

Create a `config.yaml` file for complex setups:

```yaml
input_directory: "./docs"
output_file: "./llms.txt"
mode: "full_context"
max_depth: 5
languages: ["python", "javascript", "sql"]
exclude_patterns: 
  - "*.test.md"
  - "temp/*"
  - "node_modules/*"
llm:
  enabled: true
  backend: "ollama"
  model: "llama2:7b"
  temperature: 0.3
  max_tokens: 500
logging:
  level: "INFO"
```

## Development

This project is currently under development. The basic structure is in place, but individual components are being implemented incrementally.

### Project Structure

```
src/generate_llms/
├── __init__.py
├── cli.py                 # Command-line interface
├── core/                  # Core functionality
│   ├── __init__.py
│   ├── models.py         # Data models
│   ├── config.py         # Configuration management
│   └── processor.py      # Main processor
├── scanners/             # File scanning
├── parsers/              # Content parsing
├── extractors/           # Code extraction
├── llm/                  # LLM integration
└── writers/              # Output generation
```

## License

MIT License - see LICENSE file for details.

## Contributing

Contributions are welcome! Please see CONTRIBUTING.md for guidelines.
