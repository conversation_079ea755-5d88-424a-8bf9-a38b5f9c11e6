{"settings": {"title": "Capgo InAppBrowser", "project": "/cap-go/capacitor-inappbrowser", "type": "repo", "description": "Capacitor plugin for an in-app browser with features like URL change events, two-way communication, and camera/microphone usage.", "docsRepoUrl": "https://github.com/cap-go/capacitor-inappbrowser", "folders": [], "excludeFolders": ["archive", "archived", "old", "docs/old", "deprecated", "legacy", "previous", "outdated", "superseded", "i18n/zh*", "i18n/es*", "i18n/fr*", "i18n/de*", "i18n/ja*", "i18n/ko*", "i18n/ru*", "i18n/pt*", "i18n/it*", "i18n/ar*", "i18n/hi*", "i18n/tr*", "i18n/nl*", "i18n/pl*", "i18n/sv*", "i18n/vi*", "i18n/th*", "zh-cn", "zh-tw", "zh-hk", "zh-mo", "zh-sg"], "excludeFiles": ["CHANGELOG.md", "changelog.md", "CHANGELOG.mdx", "changelog.mdx", "LICENSE.md", "license.md", "CODE_OF_CONDUCT.md", "code_of_conduct.md"], "branch": "main", "stars": 96, "trustScore": 7.8, "privateRepo": false}}