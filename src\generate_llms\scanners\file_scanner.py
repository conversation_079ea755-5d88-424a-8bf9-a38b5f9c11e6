"""
File scanning functionality for discovering documentation files.
"""

import logging
import fnmatch
from pathlib import Path
from typing import List, Optional, Set, Iterator
from dataclasses import dataclass
from abc import ABC, abstractmethod

import charset_normalizer

from ..core.models import ProcessingConfig


@dataclass
class ScanResult:
    """Result of file scanning operation."""
    
    files: List[Path]
    total_scanned: int
    excluded_count: int
    errors: List[str]
    scan_depth_reached: Optional[int] = None


class FileScanner(ABC):
    """Abstract base class for file scanning."""
    
    @abstractmethod
    def scan(self, root_path: Path, config: ProcessingConfig) -> ScanResult:
        """
        Scan directory for supported documentation files.
        
        Args:
            root_path: Root directory to scan
            config: Processing configuration
            
        Returns:
            ScanResult with discovered files and metadata
        """
        pass


class RecursiveFileScanner(FileScanner):
    """
    Recursive file scanner with depth control and pattern filtering.
    """
    
    def __init__(self):
        """Initialize the scanner."""
        self.logger = logging.getLogger(__name__)
    
    def scan(self, root_path: Path, config: ProcessingConfig) -> ScanResult:
        """
        Recursively scan directory for documentation files.
        
        Args:
            root_path: Root directory to scan
            config: Processing configuration with extensions, patterns, depth limits
            
        Returns:
            ScanResult with discovered files and scan statistics
        """
        self.logger.info(f"Starting recursive scan of {root_path}")
        
        files = []
        total_scanned = 0
        excluded_count = 0
        errors = []
        max_depth_reached = 0
        
        try:
            # Get supported extensions from config
            supported_extensions = set(config.supported_extensions)
            
            # Compile exclude patterns (combine user patterns with smart defaults)
            exclude_patterns = self._compile_exclude_patterns(config)
            
            # Determine scan paths (specific folders or entire root)
            scan_paths = self._get_scan_paths(root_path, config)

            # Perform recursive scan
            for scan_path in scan_paths:
                for file_path in self._scan_recursive(
                    scan_path,
                    supported_extensions,
                    exclude_patterns,
                    config.max_depth,
                    current_depth=0
                ):
                    total_scanned += 1
                    depth = len(file_path.relative_to(root_path).parts)
                    max_depth_reached = max(max_depth_reached, depth)

                    if self._should_include_file(file_path, exclude_patterns):
                        files.append(file_path)
                    else:
                        excluded_count += 1
                        self.logger.debug(f"Excluded file: {file_path}")
            
        except Exception as e:
            error_msg = f"Scan error: {e}"
            self.logger.error(error_msg)
            errors.append(error_msg)
        
        result = ScanResult(
            files=files,
            total_scanned=total_scanned,
            excluded_count=excluded_count,
            errors=errors,
            scan_depth_reached=max_depth_reached if max_depth_reached > 0 else None
        )
        
        self.logger.info(f"Scan completed: {len(files)} files found, {excluded_count} excluded")
        return result
    
    def _scan_recursive(
        self,
        path: Path,
        extensions: Set[str],
        exclude_patterns: List[str],
        max_depth: Optional[int],
        current_depth: int
    ) -> Iterator[Path]:
        """
        Recursively yield files matching criteria.

        Args:
            path: Current path to scan
            extensions: Set of supported file extensions
            exclude_patterns: Compiled exclude patterns
            max_depth: Maximum recursion depth (None for unlimited)
            current_depth: Current recursion depth

        Yields:
            Path objects for matching files
        """
        if max_depth is not None and current_depth >= max_depth:
            return

        try:
            if not path.exists() or not path.is_dir():
                return

            # Track visited directories to prevent infinite recursion on symlinks
            visited = getattr(self, '_visited_dirs', set())
            if not hasattr(self, '_visited_dirs'):
                self._visited_dirs = visited

            # Resolve path to handle symlinks
            try:
                resolved_path = path.resolve()
                if resolved_path in visited:
                    self.logger.debug(f"Skipping already visited directory: {path}")
                    return
                visited.add(resolved_path)
            except (OSError, RuntimeError) as e:
                self.logger.warning(f"Cannot resolve path {path}: {e}")
                return

            for item in path.iterdir():
                try:
                    # Skip if item matches exclude patterns
                    if self._matches_exclude_patterns(item, exclude_patterns):
                        self.logger.debug(f"Excluded by pattern: {item}")
                        continue

                    if item.is_file():
                        if item.suffix.lower() in extensions:
                            yield item
                    elif item.is_dir() and not item.is_symlink():
                        # Recursively scan subdirectories (skip symlinks to avoid cycles)
                        yield from self._scan_recursive(
                            item,
                            extensions,
                            exclude_patterns,
                            max_depth,
                            current_depth + 1
                        )
                except (PermissionError, OSError) as e:
                    self.logger.warning(f"Cannot access {item}: {e}")
                    continue

        except (PermissionError, OSError) as e:
            self.logger.warning(f"Cannot access directory {path}: {e}")
    
    def _compile_exclude_patterns(self, config: ProcessingConfig) -> List[str]:
        """
        Compile exclude patterns combining user patterns with smart defaults.

        Args:
            config: Processing configuration with user patterns and defaults

        Returns:
            Combined list of patterns ready for matching
        """
        patterns = []

        # Add Context7-inspired smart defaults
        patterns.extend(config.default_exclude_files)
        patterns.extend(config.default_exclude_folders)

        # Add user-specified patterns
        if config.exclude_patterns:
            patterns.extend(config.exclude_patterns)

        self.logger.debug(f"Using {len(patterns)} exclude patterns")
        return patterns

    def _should_include_file(self, file_path: Path, exclude_patterns: List[str]) -> bool:
        """
        Check if file should be included based on exclude patterns.

        Args:
            file_path: Path to check
            exclude_patterns: List of exclude patterns

        Returns:
            True if file should be included, False otherwise
        """
        if not exclude_patterns:
            return True

        return not self._matches_exclude_patterns(file_path, exclude_patterns)

    def _matches_exclude_patterns(self, path: Path, exclude_patterns: List[str]) -> bool:
        """
        Check if path matches any exclude pattern.

        Args:
            path: Path to check
            exclude_patterns: List of glob patterns

        Returns:
            True if path matches any exclude pattern
        """
        if not exclude_patterns:
            return False

        path_str = str(path)
        path_name = path.name

        for pattern in exclude_patterns:
            # Check against full path
            if fnmatch.fnmatch(path_str, pattern):
                return True
            # Check against just filename
            if fnmatch.fnmatch(path_name, pattern):
                return True
            # Check if pattern is in path (for simple substring matching)
            if pattern in path_str:
                return True

        return False

    def detect_encoding(self, file_path: Path) -> str:
        """
        Detect file encoding using charset-normalizer.

        Args:
            file_path: Path to file

        Returns:
            Detected encoding name
        """
        try:
            with open(file_path, 'rb') as f:
                # Read first 8KB for encoding detection
                raw_data = f.read(8192)

            if not raw_data:
                return 'utf-8'  # Default for empty files

            # Use charset-normalizer for detection
            detection_result = charset_normalizer.from_bytes(raw_data)
            if detection_result:
                encoding = detection_result.best().encoding
                self.logger.debug(f"Detected encoding {encoding} for {file_path}")
                return encoding
            else:
                self.logger.warning(f"Could not detect encoding for {file_path}, using utf-8")
                return 'utf-8'

        except Exception as e:
            self.logger.warning(f"Encoding detection failed for {file_path}: {e}")
            return 'utf-8'  # Fallback to UTF-8

    def _get_scan_paths(self, root_path: Path, config: ProcessingConfig) -> List[Path]:
        """
        Get paths to scan based on configuration.

        Args:
            root_path: Root directory
            config: Processing configuration with include_folders

        Returns:
            List of paths to scan
        """
        if config.include_folders:
            # Scan only specific folders
            scan_paths = []
            for folder in config.include_folders:
                folder_path = root_path / folder
                if folder_path.exists() and folder_path.is_dir():
                    scan_paths.append(folder_path)
                    self.logger.debug(f"Including folder: {folder_path}")
                else:
                    self.logger.warning(f"Include folder not found: {folder_path}")
            return scan_paths
        else:
            # Scan entire root directory
            return [root_path]
