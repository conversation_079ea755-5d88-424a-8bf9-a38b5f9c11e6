"""
Content parsing functionality for different documentation formats.
"""

import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
from abc import ABC, abstractmethod
from dataclasses import dataclass

from ..core.models import ParsedContent, ProcessingConfig


class ContentParser(ABC):
    """Abstract base class for content parsers."""
    
    @abstractmethod
    def can_parse(self, file_path: Path) -> bool:
        """
        Check if this parser can handle the given file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if parser can handle this file type
        """
        pass
    
    @abstractmethod
    def parse(self, file_path: Path, config: ProcessingConfig) -> ParsedContent:
        """
        Parse file content into structured format.
        
        Args:
            file_path: Path to file to parse
            config: Processing configuration
            
        Returns:
            ParsedContent with structured data
        """
        pass


class MarkdownParser(ContentParser):
    """Parser for Markdown files (.md, .mdx)."""
    
    def __init__(self):
        """Initialize the parser."""
        self.logger = logging.getLogger(__name__)
        self.supported_extensions = {'.md', '.mdx'}
    
    def can_parse(self, file_path: Path) -> bool:
        """Check if file is a supported Markdown format."""
        return file_path.suffix.lower() in self.supported_extensions
    
    def parse(self, file_path: Path, config: ProcessingConfig) -> ParsedContent:
        """
        Parse Markdown file content.

        Args:
            file_path: Path to Markdown file
            config: Processing configuration

        Returns:
            ParsedContent with parsed Markdown data
        """
        self.logger.debug(f"Parsing Markdown file: {file_path}")

        try:
            # Detect encoding using the scanner's method
            from ..scanners import RecursiveFileScanner
            scanner = RecursiveFileScanner()
            encoding = scanner.detect_encoding(file_path)

            # Read file content
            with open(file_path, 'r', encoding=encoding) as f:
                raw_content = f.read()

            if not raw_content.strip():
                self.logger.warning(f"Empty file: {file_path}")
                return ParsedContent(
                    file_path=file_path,
                    file_type="markdown",
                    encoding=encoding,
                    snippets=[],
                    raw_content="",
                    metadata={"parser": "markdown", "empty": True}
                )

            # Parse with python-markdown for structure analysis
            import markdown
            md = markdown.Markdown(extensions=['codehilite', 'fenced_code', 'toc'])
            html_content = md.convert(raw_content)

            content = ParsedContent(
                file_path=file_path,
                file_type="markdown",
                encoding=encoding,
                snippets=[],  # Will be populated by code extractor
                raw_content=raw_content,
                metadata={
                    "parser": "markdown",
                    "html_length": len(html_content),
                    "raw_length": len(raw_content),
                    "toc": getattr(md, 'toc', ''),
                }
            )

            self.logger.debug(f"Successfully parsed Markdown file: {file_path} ({len(raw_content)} chars)")
            return content

        except Exception as e:
            error_msg = f"Failed to parse {file_path}: {e}"
            self.logger.error(error_msg)
            return ParsedContent(
                file_path=file_path,
                file_type="markdown",
                encoding="unknown",
                error=error_msg
            )


class HTMLParser(ContentParser):
    """Parser for HTML files."""
    
    def __init__(self):
        """Initialize the parser."""
        self.logger = logging.getLogger(__name__)
        self.supported_extensions = {'.html', '.htm'}
    
    def can_parse(self, file_path: Path) -> bool:
        """Check if file is HTML."""
        return file_path.suffix.lower() in self.supported_extensions
    
    def parse(self, file_path: Path, config: ProcessingConfig) -> ParsedContent:
        """Parse HTML file content."""
        self.logger.debug(f"Parsing HTML file: {file_path}")

        try:
            # Detect encoding
            from ..scanners import RecursiveFileScanner
            scanner = RecursiveFileScanner()
            encoding = scanner.detect_encoding(file_path)

            # Read file content
            with open(file_path, 'r', encoding=encoding) as f:
                raw_content = f.read()

            if not raw_content.strip():
                return ParsedContent(
                    file_path=file_path,
                    file_type="html",
                    encoding=encoding,
                    snippets=[],
                    raw_content="",
                    metadata={"parser": "html", "empty": True}
                )

            # Parse with BeautifulSoup
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(raw_content, 'html.parser')

            # Extract useful metadata
            title = soup.find('title')
            title_text = title.get_text().strip() if title else ""

            # Count code-related elements
            code_blocks = len(soup.find_all(['pre', 'code', 'script']))

            content = ParsedContent(
                file_path=file_path,
                file_type="html",
                encoding=encoding,
                snippets=[],  # Will be populated by code extractor
                raw_content=raw_content,
                metadata={
                    "parser": "html",
                    "title": title_text,
                    "code_blocks_count": code_blocks,
                    "raw_length": len(raw_content),
                }
            )

            self.logger.debug(f"Successfully parsed HTML file: {file_path} ({code_blocks} code blocks)")
            return content

        except Exception as e:
            error_msg = f"Failed to parse HTML {file_path}: {e}"
            self.logger.error(error_msg)
            return ParsedContent(
                file_path=file_path,
                file_type="html",
                encoding="unknown",
                error=error_msg
            )


class JupyterParser(ContentParser):
    """Parser for Jupyter notebook files (.ipynb)."""
    
    def __init__(self):
        """Initialize the parser."""
        self.logger = logging.getLogger(__name__)
        self.supported_extensions = {'.ipynb'}
    
    def can_parse(self, file_path: Path) -> bool:
        """Check if file is a Jupyter notebook."""
        return file_path.suffix.lower() in self.supported_extensions
    
    def parse(self, file_path: Path, config: ProcessingConfig) -> ParsedContent:
        """Parse Jupyter notebook content."""
        self.logger.debug(f"Parsing Jupyter notebook: {file_path}")

        try:
            # Jupyter notebooks are always UTF-8 JSON
            import json
            import nbformat

            with open(file_path, 'r', encoding='utf-8') as f:
                notebook_content = f.read()

            if not notebook_content.strip():
                return ParsedContent(
                    file_path=file_path,
                    file_type="jupyter",
                    encoding="utf-8",
                    snippets=[],
                    raw_content="",
                    metadata={"parser": "jupyter", "empty": True}
                )

            # Parse notebook JSON
            try:
                notebook = nbformat.reads(notebook_content, as_version=4)
            except Exception as e:
                raise ValueError(f"Invalid notebook format: {e}")

            # Extract cells and convert to markdown-like format
            markdown_content = []
            code_cell_count = 0
            markdown_cell_count = 0

            for cell in notebook.cells:
                if cell.cell_type == 'markdown':
                    markdown_content.append(cell.source)
                    markdown_cell_count += 1
                elif cell.cell_type == 'code':
                    # Add code cell as fenced code block
                    if cell.source.strip():
                        markdown_content.append(f"```python\n{cell.source}\n```")
                        code_cell_count += 1

            # Join all content
            raw_content = '\n\n'.join(markdown_content)

            content = ParsedContent(
                file_path=file_path,
                file_type="jupyter",
                encoding="utf-8",
                snippets=[],  # Will be populated by code extractor
                raw_content=raw_content,
                metadata={
                    "parser": "jupyter",
                    "code_cells": code_cell_count,
                    "markdown_cells": markdown_cell_count,
                    "total_cells": len(notebook.cells),
                    "kernel_spec": getattr(notebook.metadata, 'kernelspec', {}).get('name', 'unknown'),
                    "raw_length": len(raw_content),
                }
            )

            self.logger.debug(f"Successfully parsed Jupyter notebook: {file_path} ({code_cell_count} code cells)")
            return content

        except Exception as e:
            error_msg = f"Failed to parse Jupyter notebook {file_path}: {e}"
            self.logger.error(error_msg)
            return ParsedContent(
                file_path=file_path,
                file_type="jupyter",
                encoding="utf-8",
                error=error_msg
            )


class RSTParser(ContentParser):
    """Parser for reStructuredText files (.rst)."""
    
    def __init__(self):
        """Initialize the parser."""
        self.logger = logging.getLogger(__name__)
        self.supported_extensions = {'.rst'}
    
    def can_parse(self, file_path: Path) -> bool:
        """Check if file is reStructuredText."""
        return file_path.suffix.lower() in self.supported_extensions
    
    def parse(self, file_path: Path, config: ProcessingConfig) -> ParsedContent:
        """Parse reStructuredText content."""
        self.logger.debug(f"Parsing RST file: {file_path}")

        try:
            # Detect encoding
            from ..scanners import RecursiveFileScanner
            scanner = RecursiveFileScanner()
            encoding = scanner.detect_encoding(file_path)

            # Read file content
            with open(file_path, 'r', encoding=encoding) as f:
                raw_content = f.read()

            if not raw_content.strip():
                return ParsedContent(
                    file_path=file_path,
                    file_type="rst",
                    encoding=encoding,
                    snippets=[],
                    raw_content="",
                    metadata={"parser": "rst", "empty": True}
                )

            # Parse with docutils
            from docutils.core import publish_doctree
            from docutils.parsers.rst import Parser
            from docutils.utils import new_document
            from docutils.frontend import OptionParser

            # Parse RST to document tree
            try:
                doctree = publish_doctree(raw_content)

                # Count code blocks and literal blocks
                code_blocks = len([node for node in doctree.traverse()
                                 if node.tagname in ['literal_block', 'code_block']])

            except Exception as e:
                self.logger.warning(f"RST parsing failed, using raw content: {e}")
                code_blocks = 0

            content = ParsedContent(
                file_path=file_path,
                file_type="rst",
                encoding=encoding,
                snippets=[],  # Will be populated by code extractor
                raw_content=raw_content,
                metadata={
                    "parser": "rst",
                    "code_blocks_count": code_blocks,
                    "raw_length": len(raw_content),
                }
            )

            self.logger.debug(f"Successfully parsed RST file: {file_path} ({code_blocks} code blocks)")
            return content

        except Exception as e:
            error_msg = f"Failed to parse RST {file_path}: {e}"
            self.logger.error(error_msg)
            return ParsedContent(
                file_path=file_path,
                file_type="rst",
                encoding="unknown",
                error=error_msg
            )


class AsciiDocParser(ContentParser):
    """Parser for AsciiDoc files (.adoc)."""
    
    def __init__(self):
        """Initialize the parser."""
        self.logger = logging.getLogger(__name__)
        self.supported_extensions = {'.adoc'}
    
    def can_parse(self, file_path: Path) -> bool:
        """Check if file is AsciiDoc."""
        return file_path.suffix.lower() in self.supported_extensions
    
    def parse(self, file_path: Path, config: ProcessingConfig) -> ParsedContent:
        """Parse AsciiDoc content."""
        self.logger.debug(f"Parsing AsciiDoc file: {file_path}")

        try:
            # Detect encoding
            from ..scanners import RecursiveFileScanner
            scanner = RecursiveFileScanner()
            encoding = scanner.detect_encoding(file_path)

            # Read file content
            with open(file_path, 'r', encoding=encoding) as f:
                raw_content = f.read()

            if not raw_content.strip():
                return ParsedContent(
                    file_path=file_path,
                    file_type="asciidoc",
                    encoding=encoding,
                    snippets=[],
                    raw_content="",
                    metadata={"parser": "asciidoc", "empty": True}
                )

            # Simple analysis of AsciiDoc content
            # Count code blocks (lines starting with ---- or [source)
            lines = raw_content.split('\n')
            code_block_markers = 0
            source_blocks = 0

            for line in lines:
                stripped = line.strip()
                if stripped.startswith('----') or stripped.startswith('....'):
                    code_block_markers += 1
                elif stripped.startswith('[source'):
                    source_blocks += 1

            # Estimate code blocks (pairs of markers)
            estimated_code_blocks = (code_block_markers // 2) + source_blocks

            content = ParsedContent(
                file_path=file_path,
                file_type="asciidoc",
                encoding=encoding,
                snippets=[],  # Will be populated by code extractor
                raw_content=raw_content,
                metadata={
                    "parser": "asciidoc",
                    "estimated_code_blocks": estimated_code_blocks,
                    "source_blocks": source_blocks,
                    "raw_length": len(raw_content),
                }
            )

            self.logger.debug(f"Successfully parsed AsciiDoc file: {file_path} (~{estimated_code_blocks} code blocks)")
            return content

        except Exception as e:
            error_msg = f"Failed to parse AsciiDoc {file_path}: {e}"
            self.logger.error(error_msg)
            return ParsedContent(
                file_path=file_path,
                file_type="asciidoc",
                encoding="unknown",
                error=error_msg
            )


class ContentParserFactory:
    """Factory for creating appropriate content parsers."""
    
    def __init__(self):
        """Initialize the factory with available parsers."""
        self.parsers = [
            MarkdownParser(),
            HTMLParser(),
            JupyterParser(),
            RSTParser(),
            AsciiDocParser(),
        ]
        self.logger = logging.getLogger(__name__)
    
    def get_parser(self, file_path: Path) -> Optional[ContentParser]:
        """
        Get appropriate parser for the given file.
        
        Args:
            file_path: Path to file
            
        Returns:
            ContentParser instance or None if no parser available
        """
        for parser in self.parsers:
            if parser.can_parse(file_path):
                return parser
        
        self.logger.warning(f"No parser available for file: {file_path}")
        return None
    
    def parse_file(self, file_path: Path, config: ProcessingConfig) -> Optional[ParsedContent]:
        """
        Parse file using appropriate parser.
        
        Args:
            file_path: Path to file to parse
            config: Processing configuration
            
        Returns:
            ParsedContent or None if parsing failed
        """
        parser = self.get_parser(file_path)
        if parser is None:
            return None
        
        return parser.parse(file_path, config)
