= Test AsciiDoc Document

This is a test AsciiDoc document with code examples.

== Python Example

Here's a Python code block:

[source,python]
----
def hello_world():
    """Print hello world."""
    print("Hello, World!")
    return True
----

== Shell Commands

Some shell commands:

....
$ pip install generate-llms
$ generate-llms --help
....

== JavaScript Example

[source,javascript]
----
function greet(name) {
    console.log(`Hello, ${name}!`);
    return name;
}
----
