========================
CODE SNIPPETS
========================
TITLE: Run Example with npm start
DESCRIPTION: This command initiates the execution of the example application. It's the primary way to get the project running and see its functionality.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/example-app/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm start
```

----------------------------------------

TITLE: Install Dependencies
DESCRIPTION: Installs the necessary Node.js dependencies for the project.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/CONTRIBUTING.md#_snippet_0

LANGUAGE: shell
CODE:
```
npm install
```

----------------------------------------

TITLE: Install SwiftLint (macOS)
DESCRIPTION: Installs SwiftLint using Homebrew, a tool for enforcing Swift style and conventions.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/CONTRIBUTING.md#_snippet_1

LANGUAGE: shell
CODE:
```
brew install swiftlint
```

----------------------------------------

TITLE: Install @capgo/inappbrowser
DESCRIPTION: Installs the @capgo/inappbrowser package using npm and synchronizes Capacitor plugins.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install @capgo/inappbrowser
npx cap sync
```

----------------------------------------

TITLE: Cross-Platform Button Near Done Configuration
DESCRIPTION: Example of configuring the custom button near 'done' for both iOS and Android platforms simultaneously.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/buttonNearDone.md#_snippet_2

LANGUAGE: ts
CODE:
```
InAppBrowser.openWebView({
  url: WEB_URL,
  buttonNearDone: {
    ios: { icon: 'monkey', iconType: 'resource' },
    android: { icon: 'public/monkey.svg', iconType: 'asset' }
  }
})
```

----------------------------------------

TITLE: Get Cookie Options
DESCRIPTION: Defines the options for retrieving cookies from the InAppBrowser, including the URL and an option to include HTTP-only cookies.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_24

LANGUAGE: APIDOC
CODE:
```
GetCookieOptions:
  url: string
  includeHttpOnly: boolean
```

----------------------------------------

TITLE: Get Cookies
DESCRIPTION: Retrieves cookies associated with a specific URL within the InAppBrowser.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_14

LANGUAGE: APIDOC
CODE:
```
getCookies(options: GetCookieOptions)
  - Retrieves cookies for a given URL.
  - Parameters:
    - options: An object of type GetCookieOptions, containing the URL.
  - Returns: A Promise that resolves with a Record<string, string> representing the cookies.
```

----------------------------------------

TITLE: Authorized App Links Configuration (InAppBrowser - Android)
DESCRIPTION: Specifies a list of URL base patterns that are treated as authorized App Links on Android. Only links starting with these base URLs will be opened within the InAppBrowser. This is an Android-only feature.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_34

LANGUAGE: typescript
CODE:
```
authorizedAppLinks: string[];
```

LANGUAGE: json
CODE:
```
{
  "authorizedAppLinks": []
}
```

----------------------------------------

TITLE: Publish Plugin
DESCRIPTION: Publishes the plugin to the npm registry. A 'prepublishOnly' hook in package.json ensures the plugin is prepared before publishing.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/CONTRIBUTING.md#_snippet_5

LANGUAGE: shell
CODE:
```
npm publish
```

----------------------------------------

TITLE: Verify Plugin Build
DESCRIPTION: Builds and validates both the web and native projects. This script is useful for CI to ensure the plugin builds correctly across all supported platforms.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/CONTRIBUTING.md#_snippet_3

LANGUAGE: shell
CODE:
```
npm run verify
```

----------------------------------------

TITLE: InAppBrowser API Documentation
DESCRIPTION: Provides documentation for the InAppBrowser API, including methods for opening, closing, managing cookies and cache, executing scripts, and handling events.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_11

LANGUAGE: APIDOC
CODE:
```
open(options: OpenOptions) => Promise<any>
  Opens url in a new window fullscreen. On Android, it uses Chrome Custom Tabs; on iOS, it uses SFSafariViewController.
  Parameters:
    options: OpenOptions - An object containing options for opening the browser.
  Returns:
    Promise<any> - A promise that resolves with any result from the browser.
  Since: 0.1.0

clearCookies() => Promise<any>
  Clears all cookies for the InAppBrowser.
  Returns:
    Promise<any> - A promise that resolves when cookies are cleared.

clearAllCookies() => Promise<any>
  Clears all cookies for all domains.
  Returns:
    Promise<any> - A promise that resolves when all cookies are cleared.

clearCache() => Promise<any>
  Clears the browser cache.
  Returns:
    Promise<any> - A promise that resolves when the cache is cleared.

getCookies(options: CookieOptions) => Promise<any>
  Retrieves cookies for a specific URL.
  Parameters:
    options: CookieOptions - An object specifying the URL to get cookies for.
  Returns:
    Promise<any> - A promise that resolves with the cookies.

close() => Promise<any>
  Closes the InAppBrowser.
  Returns:
    Promise<any> - A promise that resolves when the browser is closed.

openWebView(options: OpenWebViewOptions) => Promise<any>
  Opens a WebView with a specified URL and optional safe margin.
  Parameters:
    options: OpenWebViewOptions - An object containing options for the WebView.
  Returns:
    Promise<any> - A promise that resolves with any result from the WebView.

executeScript(options: ExecuteScriptOptions) => Promise<any>
  Executes a JavaScript script within the InAppBrowser.
  Parameters:
    options: ExecuteScriptOptions - An object containing the script to execute.
  Returns:
    Promise<any> - A promise that resolves with the result of the script execution.

postMessage(options: PostMessageOptions) => Promise<any>
  Sends a message to the InAppBrowser or from the InAppBrowser to the main app.
  Parameters:
    options: PostMessageOptions - An object containing the message payload.
  Returns:
    Promise<any> - A promise that resolves upon message posting.

setUrl(options: SetUrlOptions) => Promise<any>
  Sets the URL of the currently open InAppBrowser.
  Parameters:
    options: SetUrlOptions - An object containing the new URL.
  Returns:
    Promise<any> - A promise that resolves when the URL is set.

addListener(eventName: 'urlChangeEvent', listenerFunc: (event: { url: string }) => void) => Promise<PluginListenerHandle>
  Listens for URL change events in the InAppBrowser.
  Parameters:
    eventName: 'urlChangeEvent' - The name of the event to listen for.
    listenerFunc: (event: { url: string }) => void - The callback function to execute when the event occurs.
  Returns:
    Promise<PluginListenerHandle> - A promise that resolves with a handle to remove the listener.

addListener(eventName: 'buttonNearDoneClick', listenerFunc: () => void) => Promise<PluginListenerHandle>
  Listens for the 'buttonNearDoneClick' event.
  Parameters:
    eventName: 'buttonNearDoneClick' - The name of the event to listen for.
    listenerFunc: () => void - The callback function to execute.
  Returns:
    Promise<PluginListenerHandle> - A promise that resolves with a handle to remove the listener.

addListener(eventName: 'closeEvent', listenerFunc: () => void) => Promise<PluginListenerHandle>
  Listens for the 'closeEvent' event.
  Parameters:
    eventName: 'closeEvent' - The name of the event to listen for.
    listenerFunc: () => void - The callback function to execute.
  Returns:
    Promise<PluginListenerHandle> - A promise that resolves with a handle to remove the listener.

addListener(eventName: 'confirmBtnClicked', listenerFunc: (event: { data: any }) => void) => Promise<PluginListenerHandle>
  Listens for the 'confirmBtnClicked' event.
  Parameters:
    eventName: 'confirmBtnClicked' - The name of the event to listen for.
    listenerFunc: (event: { data: any }) => void - The callback function to execute with event data.
  Returns:
    Promise<PluginListenerHandle> - A promise that resolves with a handle to remove the listener.

addListener(eventName: 'messageFromWebview', listenerFunc: (event: { data: any }) => void) => Promise<PluginListenerHandle>
  Listens for messages sent from the InAppBrowser to the main app.
  Parameters:
    eventName: 'messageFromWebview' - The name of the event to listen for.
    listenerFunc: (event: { data: any }) => void - The callback function to execute with message data.
  Returns:
    Promise<PluginListenerHandle> - A promise that resolves with a handle to remove the listener.

addListener(eventName: 'browserPageLoaded', listenerFunc: (event: { url: string }) => void) => Promise<PluginListenerHandle>
  Listens for the 'browserPageLoaded' event.
  Parameters:
    eventName: 'browserPageLoaded' - The name of the event to listen for.
    listenerFunc: (event: { url: string }) => void - The callback function to execute with the loaded URL.
  Returns:
    Promise<PluginListenerHandle> - A promise that resolves with a handle to remove the listener.

addListener(eventName: 'pageLoadError', listenerFunc: (event: { url: string; error: string }) => void) => Promise<PluginListenerHandle>
  Listens for page load error events.
  Parameters:
    eventName: 'pageLoadError' - The name of the event to listen for.
    listenerFunc: (event: { url: string; error: string }) => void - The callback function to execute with error details.
  Returns:
    Promise<PluginListenerHandle> - A promise that resolves with a handle to remove the listener.

removeAllListeners() => Promise<void>
  Removes all registered listeners.
  Returns:
    Promise<void>

reload() => Promise<any>
  Reloads the current page in the InAppBrowser.
  Returns:
    Promise<any> - A promise that resolves when the page is reloaded.
```

----------------------------------------

TITLE: InAppBrowser API Configuration Options
DESCRIPTION: Details the configuration options for the InAppBrowser, specifically focusing on the `buttonNearDone` feature for iOS and Android.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/buttonNearDone.md#_snippet_4

LANGUAGE: APIDOC
CODE:
```
InAppBrowser.openWebView(options: {
  url: string,
  buttonNearDone?: {
    ios?: {
      iconType: 'sf-symbol' | 'asset',
      icon: string
    },
    android?: {
      iconType: 'asset',
      icon: string,
      width?: number,
      height?: number
    }
  }
}): Promise<void>

// Parameters for buttonNearDone.ios:
// iconType: Specifies whether to use an SF Symbol ('sf-symbol') or a file from Assets.xcassets ('asset').
// icon: The name or path of the icon to use.

// Parameters for buttonNearDone.android:
// iconType: Must be 'asset'. Reads from the main assets of the app.
// icon: The path of the icon to use (e.g., 'public/your_file.svg').
// width: Optional width of the button. Defaults to 48.
// height: Optional height of the button. Defaults to 48.

// Event Listener:
InAppBrowser.addListener('buttonNearDoneClick', callback: (msg: any) => Promise<void>): Promise<void>
// Listens for clicks on the custom button near the 'done' button.
```

----------------------------------------

TITLE: Build Plugin
DESCRIPTION: Builds the plugin's web assets and generates API documentation using @capacitor/docgen. It compiles TypeScript to ESM JavaScript and bundles it for use with or without bundlers.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/CONTRIBUTING.md#_snippet_2

LANGUAGE: shell
CODE:
```
npm run build
```

----------------------------------------

TITLE: InAppBrowser Options
DESCRIPTION: Defines the configuration options for opening the InAppBrowser. Includes the target URL, whether to present the browser after page load, and whether to prevent deep linking.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_21

LANGUAGE: APIDOC
CODE:
```
OpenOptions:
  url: string (Target URL to load.)
  isPresentAfterPageLoad: boolean (if true, the browser will be presented after the page is loaded, if false, the browser will be presented immediately.)
  preventDeeplink: boolean (if true the deeplink will not be opened, if false the deeplink will be opened when clicked on the link)
```

----------------------------------------

TITLE: Capacitor InAppBrowser Options
DESCRIPTION: Provides a detailed overview of the configuration options available for the Capacitor InAppBrowser plugin. This includes settings for JavaScript interfaces, sharing disclaimers, toolbar customization, and visual appearance.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_26

LANGUAGE: APIDOC
CODE:
```
jsInterface:
  Description: The webview automatically injects a JavaScript interface providing:
    - window.mobileApp.close(): Closes the webview from JavaScript
    - window.mobileApp.postMessage(obj): Sends a message to the app (listen via "messageFromWebview" event)

shareDisclaimer:
  Type: DisclaimerOptions
  Description: Share options for the webview. When provided, shows a disclaimer dialog before sharing content. This is useful for:
    - Warning users about sharing sensitive information
    - Getting user consent before sharing
    - Explaining what will be shared
    - Complying with privacy regulations
  Note: shareSubject is required when using shareDisclaimer

toolbarType:
  Type: ToolBarType
  Description: Toolbar type determines the appearance and behavior of the browser's toolbar.
  Values:
    - "activity": Shows a simple toolbar with just a close button and share button
    - "navigation": Shows a full navigation toolbar with back/forward buttons
    - "blank": Shows no toolbar
    - "": Default toolbar with close button
  Default: ToolBarType.DEFAULT

shareSubject:
  Type: string
  Description: Subject text for sharing. Required when using shareDisclaimer. This text will be used as the subject line when sharing content.

title:
  Type: string
  Description: Title of the browser
  Default: 'New Window'

backgroundColor:
  Type: BackgroundColor
  Description: Background color of the browser
  Default: BackgroundColor.BLACK
```

----------------------------------------

TITLE: Capacitor InAppBrowser Configuration Options
DESCRIPTION: This section details various configuration options for the Capacitor InAppBrowser plugin. These options control the appearance and behavior of the in-app browser window.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_30

LANGUAGE: APIDOC
CODE:
```
toolbarColor:
  type: string
  description: Color of the toolbar in hex format.
  default: '#ffffff'
  since: 1.2.5

toolbarTextColor:
  type: string
  description: Color of the buttons and title in the toolbar in hex format. When set, it overrides the automatic light/dark mode detection for text color.
  default: 'calculated based on toolbarColor brightness'
  since: 6.10.0

showArrow:
  type: boolean
  description: If true, an arrow will be shown instead of a cross for closing the window.
  default: false
  since: 1.2.5

ignoreUntrustedSSLError:
  type: boolean
  description: If true, the webview will ignore untrusted SSL errors, allowing the user to view the website.
  default: false
  since: 6.1.0

preShowScript:
  type: string
  description: If isPresentAfterPageLoad is true and this variable is set, the plugin will inject a script before showing the browser. This script will be run in an async context. The plugin will wait for the script to finish (max 10 seconds).
  since: 6.6.0

proxyRequests:
  type: string
  description: proxyRequests is a regex expression. Please see https://github.com/Cap-go/capacitor-inappbrowser/pull/222 for more info. (Android only)
  since: 6.9.0
```

----------------------------------------

TITLE: iOS Camera Usage Description
DESCRIPTION: Adds the camera usage description to the Info.plist file for iOS.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_4

LANGUAGE: xml
CODE:
```
<key>NSCameraUsageDescription</key>
<string>We need access to the camera to record audio.</string>
```

----------------------------------------

TITLE: Lint and Format Code
DESCRIPTION: Checks code formatting and quality using ESLint and Prettier, and automatically formats or fixes issues. SwiftLint is also integrated for macOS users.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/CONTRIBUTING.md#_snippet_4

LANGUAGE: shell
CODE:
```
npm run lint
```

LANGUAGE: shell
CODE:
```
npm run fmt
```

----------------------------------------

TITLE: Open InAppBrowser
DESCRIPTION: Opens the InAppBrowser with a specified URL.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_1

LANGUAGE: javascript
CODE:
```
import { InAppBrowser } from '@capgo/inappbrowser'

InAppBrowser.open({ url: "YOUR_URL" });
```

----------------------------------------

TITLE: Credentials
DESCRIPTION: Defines the structure for authentication credentials, including username and password.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_37

LANGUAGE: APIDOC
CODE:
```
Credentials:
  username: string
  password: string
```

----------------------------------------

TITLE: OpenWebViewOptions API
DESCRIPTION: Defines the options available when opening a web view using the InAppBrowser plugin. This includes the target URL, request headers, credentials, and specific Android UI settings.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_25

LANGUAGE: APIDOC
CODE:
```
OpenWebViewOptions:
  url: string
    Target URL to load.
  headers: Headers
    Headers to send with the request.
  credentials: Credentials
    Credentials to send with the request and all subsequent requests for the same host.
  materialPicker: boolean
    If true, uses Material Design theme for date and time pickers on Android. This improves the appearance of HTML date inputs to use modern Material Design UI instead of the old style pickers.
    Default: false
```

----------------------------------------

TITLE: Android Permissions for Camera and Microphone
DESCRIPTION: Adds necessary permissions to the AndroidManifest.xml for camera and microphone access.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_3

LANGUAGE: xml
CODE:
```
<uses-permission android:name="android.permission.CAMERA" />
		<uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
		<uses-permission android:name="android.permission.RECORD_AUDIO"/>
```

----------------------------------------

TITLE: iOS Microphone Usage Description
DESCRIPTION: Adds the microphone usage description to the Info.plist file for iOS.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_5

LANGUAGE: xml
CODE:
```
<key>NSMicrophoneUsageDescription</key>
<string>We need access to the microphone to record audio.</string>
```

----------------------------------------

TITLE: Capacitor InAppBrowser Configuration Options
DESCRIPTION: This section details the various boolean configuration options for the Capacitor InAppBrowser plugin. These options control the behavior and appearance of the in-app browser, with some settings being platform-specific (Android or iOS).

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_27

LANGUAGE: APIDOC
CODE:
```
activeNativeNavigationForWebview:
  type: boolean
  description: If true, activates native navigation within the webview. Android only.
  default: false

disableGoBackOnNativeApplication:
  type: boolean
  description: Disables the possibility to go back on the native application, useful to force the user to stay on the webview. Android only.
  default: false

isPresentAfterPageLoad:
  type: boolean
  description: If true, the browser will be presented after the page is loaded. If false, the browser will be presented immediately. Affects new window presentation.
  default: false
  since: 0.1.0

isInspectable:
  type: boolean
  description: Determines whether the website in the webview is inspectable or not. iOS only.
  default: false

isAnimated:
  type: boolean
  description: Controls whether the webview opening is animated. iOS only.
  default: true

showReloadButton:
  type: boolean
  description: Shows a reload button that reloads the web page.
  default: false
  since: 1.0.15
```

----------------------------------------

TITLE: Webview Control
DESCRIPTION: Methods for closing the current webview and opening a new one with advanced capabilities.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_15

LANGUAGE: APIDOC
CODE:
```
close()
  - Closes the currently active webview.
  - Returns: A Promise that resolves with any value.

openWebView(options: OpenWebViewOptions)
  - Opens a new webview with specified options, including toolbars and enhanced capabilities like camera access, file access, event listening, JavaScript injection, and bidirectional communication.
  - JavaScript Interface:
    - window.mobileApp.close(): Closes the webview from within JavaScript.
    - window.mobileApp.postMessage({detail: {message: 'myMessage'}}): Sends a message from the webview to the native app.
  - Parameters:
    - options: An object of type OpenWebViewOptions.
  - Returns: A Promise that resolves with any value.
  - Since: 0.1.0
```

----------------------------------------

TITLE: Listening for Button Near Done Click Events
DESCRIPTION: Adds a listener to capture 'buttonNearDoneClick' events and execute custom logic, such as navigating to a new URL.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/buttonNearDone.md#_snippet_3

LANGUAGE: ts
CODE:
```
InAppBrowser.addListener('buttonNearDoneClick', async (msg) => {
  // Write your code here
  await InAppBrowser.setUrl({ url: 'https://web.capgo.app/login' })
})
```

----------------------------------------

TITLE: JavaScript Execution
DESCRIPTION: Allows for the injection and execution of arbitrary JavaScript code within the InAppBrowser window.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_16

LANGUAGE: APIDOC
CODE:
```
executeScript({ code }: { code: string })
  - Injects and executes JavaScript code in the InAppBrowser.
  - Parameters:
    - code: A string containing the JavaScript code to execute.
  - Returns: A Promise that resolves with void.
```

----------------------------------------

TITLE: DisclaimerOptions
DESCRIPTION: Options for configuring the disclaimer dialog displayed by the InAppBrowser. Includes title, message, and button texts.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_38

LANGUAGE: APIDOC
CODE:
```
DisclaimerOptions:
  title?: string
  message?: string
  confirmBtn?: string
  cancelBtn?: string
```

----------------------------------------

TITLE: Open WebView with Safe Margin
DESCRIPTION: Opens a WebView with a safe bottom margin enabled.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_2

LANGUAGE: javascript
CODE:
```
import { InAppBrowser } from '@capgo/inappbrowser'

InAppBrowser.openWebView({
  url: "YOUR_URL",
  enabledSafeBottomMargin: true
});
```

----------------------------------------

TITLE: URL Change Listener
DESCRIPTION: Provides a mechanism to listen for URL changes within an opened webview.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_19

LANGUAGE: APIDOC
CODE:
```
addListener('urlChangeEvent', listenerFunc: UrlChangeListener)
  - Listens for URL changes in the webview. This is applicable only when `openWebView` is used.
  - Parameters:
    - eventName: The specific event name, which must be 'urlChangeEvent'.
    - listenerFunc: A callback function of type `UrlChangeListener` that will be executed when the URL changes.
  - Returns: A Promise that resolves with a `PluginListenerHandle`.
  - Since: 0.0.1
```

----------------------------------------

TITLE: ToolBarType Enum
DESCRIPTION: Defines the types of toolbars that can be displayed in the InAppBrowser. Options include ACTIVITY, COMPACT, NAVIGATION, and BLANK.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_43

LANGUAGE: APIDOC
CODE:
```
ToolBarType:
  ACTIVITY = "activity"
  COMPACT = "compact"
  NAVIGATION = "navigation"
  BLANK = "blank"
```

----------------------------------------

TITLE: Message Posting
DESCRIPTION: Enables sending messages from the native application to the webview, facilitating communication.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_17

LANGUAGE: APIDOC
CODE:
```
postMessage(options: { detail: Record<string, any> })
  - Sends an event (message) to the InAppBrowser webview.
  - Event Listening in Webview: Use `window.addEventListener("messageFromNative", listenerFunc: (event: Record<string, any>) => void)`.
  - Parameters:
    - options: An object containing a `detail` property, which is a Record<string, any> representing the data to send. The data must be JSON-serializable.
  - Returns: A Promise that resolves with void.
```

----------------------------------------

TITLE: PluginListenerHandle
DESCRIPTION: Represents a handle for a plugin listener, providing a method to remove the listener.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_39

LANGUAGE: APIDOC
CODE:
```
PluginListenerHandle:
  remove: () => Promise<void>
```

----------------------------------------

TITLE: Capacitor InAppBrowser Event Listeners
DESCRIPTION: This section covers various event listeners that can be added to the InAppBrowser plugin. These listeners allow your application to react to specific user interactions or browser states within the in-app browser window.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_20

LANGUAGE: APIDOC
CODE:
```
addListener(eventName: "buttonNearDoneClick", listenerFunc: ButtonNearListener) => Promise<PluginListenerHandle>
  - Listens for the 'buttonNearDoneClick' event.
  - Parameters:
    - eventName: The name of the event, must be 'buttonNearDoneClick'.
    - listenerFunc: The callback function to execute when the event occurs.
  - Returns: A Promise that resolves with a PluginListenerHandle.

addListener(eventName: "closeEvent", listenerFunc: UrlChangeListener) => Promise<PluginListenerHandle>
  - Listens for the 'closeEvent' event, specifically for openWebView.
  - Parameters:
    - eventName: The name of the event, must be 'closeEvent'.
    - listenerFunc: The callback function to execute when the event occurs.
  - Returns: A Promise that resolves with a PluginListenerHandle.
  - Since: 0.4.0

addListener(eventName: "confirmBtnClicked", listenerFunc: ConfirmBtnListener) => Promise<PluginListenerHandle>
  - Listens for the 'confirmBtnClicked' event, triggered when the user clicks the confirm button for a disclaimer.
  - Parameters:
    - eventName: The name of the event, must be 'confirmBtnClicked'.
    - listenerFunc: The callback function to execute when the event occurs.
  - Returns: A Promise that resolves with a PluginListenerHandle.
  - Since: 0.0.1

addListener(eventName: "messageFromWebview", listenerFunc: (event: { detail: Record<string, any>; }) => void) => Promise<PluginListenerHandle>
  - Listens for messages sent from the webview to the main app.
  - To send an event to the main app, use `window.mobileApp.postMessage({ "detail": { "message": "myMessage" } })` within the webview.
  - The `detail` property is the data to send, which must be JSON serializable.
  - Parameters:
    - eventName: The name of the event, must be 'messageFromWebview'.
    - listenerFunc: The callback function to execute with the event data.
  - Returns: A Promise that resolves with a PluginListenerHandle.

addListener(eventName: "browserPageLoaded", listenerFunc: () => void) => Promise<PluginListenerHandle>
  - Listens for the 'browserPageLoaded' event, triggered when a page finishes loading.
  - Parameters:
    - eventName: The name of the event, must be 'browserPageLoaded'.
    - listenerFunc: The callback function to execute when the page is loaded.
  - Returns: A Promise that resolves with a PluginListenerHandle.

addListener(eventName: "pageLoadError", listenerFunc: () => void) => Promise<PluginListenerHandle>
  - Listens for the 'pageLoadError' event, triggered when a page fails to load.
  - Parameters:
    - eventName: The name of the event, must be 'pageLoadError'.
    - listenerFunc: The callback function to execute when a page load error occurs.
  - Returns: A Promise that resolves with a PluginListenerHandle.

removeAllListeners() => Promise<void>
  - Removes all listeners registered for this plugin.
  - Returns: A Promise that resolves when all listeners are removed.
  - Since: 1.0.0

reload() => Promise<any>
  - Reloads the current web page displayed in the in-app browser.
  - Returns: A Promise that resolves with any return value from the reload operation.
  - Since: 1.0.0
```

----------------------------------------

TITLE: UrlEvent
DESCRIPTION: An event emitted when the URL within the InAppBrowser changes. Contains the new URL.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_40

LANGUAGE: APIDOC
CODE:
```
UrlEvent:
  url: string
```

----------------------------------------

TITLE: InAppBrowser Close Modal Configuration
DESCRIPTION: Configures the behavior of the confirmation modal when the user attempts to close the InAppBrowser. Options include whether to display a confirmation, the title, description, and button texts for the modal.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_28

LANGUAGE: APIDOC
CODE:
```
closeModal: boolean
  Description: If true, a confirm dialog will be displayed when the user clicks the close button. If false, the browser will be closed immediately.
  Default: false
  Version: 1.1.0

closeModalTitle: string
  Description: The title of the confirmation dialog when the user clicks the close button.
  Default: 'Close'
  Version: 1.1.0

closeModalDescription: string
  Description: The description text within the confirmation dialog when the user clicks the close button.
  Default: 'Are you sure you want to close this window?'
  Version: 1.1.0

closeModalOk: string
  Description: The text displayed on the confirmation button in the dialog.
  Default: 'Close'
  Version: 1.1.0

closeModalCancel: string
  Description: The text displayed on the cancel button in the dialog.
  Default: 'Cancel'
  Version: 1.1.0
```

----------------------------------------

TITLE: BtnEvent
DESCRIPTION: An event emitted when a button within the InAppBrowser is clicked. Contains the URL associated with the button click.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_41

LANGUAGE: APIDOC
CODE:
```
BtnEvent:
  url: string
```

----------------------------------------

TITLE: Send Message from InAppBrowser to Main App
DESCRIPTION: Sends a message from the InAppBrowser back to the main application. The 'detail' object is mandatory and should contain JSON-serializable data.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_8

LANGUAGE: javascript
CODE:
```
window.mobileApp.postMessage({ detail: { message: "myMessage" } });
```

----------------------------------------

TITLE: BackgroundColor Enum
DESCRIPTION: Defines the available background colors for the InAppBrowser toolbar.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_44

LANGUAGE: APIDOC
CODE:
```
BackgroundColor:
  WHITE = "white"
  BLACK = "black"
```

----------------------------------------

TITLE: Text Zoom Configuration (InAppBrowser)
DESCRIPTION: Sets the text zoom level for the InAppBrowser content in percentage. This allows users to adjust text size for better readability. The default value is 100%.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_32

LANGUAGE: typescript
CODE:
```
textZoom: number;
```

LANGUAGE: json
CODE:
```
{
  "textZoom": 100
}
```

----------------------------------------

TITLE: Android Button Near Done Configuration (Asset)
DESCRIPTION: Configures a custom button near the 'done' button on Android using an SVG file from the public directory. Requires the icon path and type 'asset'.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/buttonNearDone.md#_snippet_1

LANGUAGE: ts
CODE:
```
InAppBrowser.openWebView({
  url: WEB_URL,
  buttonNearDone: {
    android: { icon: 'public/monkey.svg', iconType: 'asset' }
  }
})
```

----------------------------------------

TITLE: Cache Management
DESCRIPTION: Provides a method to clear the cache used by the InAppBrowser.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_13

LANGUAGE: APIDOC
CODE:
```
clearCache()
  - Clears the InAppBrowser's cache.
  - Returns: A Promise that resolves with any value.
  - Since: 6.5.0
```

----------------------------------------

TITLE: iOS Button Near Done Configuration (Asset)
DESCRIPTION: Configures a custom button near the 'done' button on iOS using an asset from Assets.xcassets. Requires the icon name and type 'asset'.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/buttonNearDone.md#_snippet_0

LANGUAGE: ts
CODE:
```
InAppBrowser.openWebView({
  url: WEB_URL,
  buttonNearDone: {
    ios: { icon: 'monkey', iconType: 'asset' },
  }
})
```

----------------------------------------

TITLE: Custom Toolbar Button Configuration (InAppBrowser)
DESCRIPTION: Defines a custom button that can be displayed near the done/close button in the InAppBrowser toolbar. This button is only visible when the toolbarType is not 'activity', 'navigation', or 'blank'. Platform-specific configurations for icon type, path, and dimensions are supported.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_31

LANGUAGE: typescript
CODE:
```
{
  ios: {
    iconType: 'sf-symbol' | 'asset';
    icon: string;
  };
  android: {
    iconType: 'asset' | 'vector';
    icon: string;
    width?: number;
    height?: number;
  };
}
```

----------------------------------------

TITLE: Type Aliases
DESCRIPTION: Various type aliases used within the plugin for defining complex types, including utility types like Omit, Pick, Exclude, and Record.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_42

LANGUAGE: APIDOC
CODE:
```
ClearCookieOptions = Omit<HttpCookie, 'key' | 'value'>

Omit<T, K> = Pick<T, Exclude<keyof T, K>>

Pick<T, K> = { [P in K]: T[P] }

Exclude<T, U> = T extends U ? never : T

Record<K, T> = { [P in K]: T }

GetCookieOptions = Omit<HttpCookie, 'key' | 'value'>

UrlChangeListener = (state: UrlEvent) => void

ButtonNearListener = (state: object) => void

ConfirmBtnListener = (state: BtnEvent) => void
```

----------------------------------------

TITLE: Google Pay Support Configuration (InAppBrowser)
DESCRIPTION: Enables or disables support for Google Pay popups and the Payment Request API. Enabling this option can resolve certain errors by allowing popup windows and configuring Cross-Origin-Opener-Policy. It is recommended to enable this only if Google Pay functionality is required, as it permits popup windows and configures necessary headers for payment processing.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_36

LANGUAGE: typescript
CODE:
```
enableGooglePaySupport: boolean;
```

LANGUAGE: json
CODE:
```
{
  "enableGooglePaySupport": false
}
```

----------------------------------------

TITLE: Send Message from Main App to InAppBrowser
DESCRIPTION: Sends a message from the main application to the InAppBrowser. The 'detail' object is mandatory and should contain JSON-serializable data.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_6

LANGUAGE: javascript
CODE:
```
InAppBrowser.postMessage({ detail: { message: "myMessage" } });
```

----------------------------------------

TITLE: Receive Event from InAppBrowser in Main App
DESCRIPTION: Listens for events sent from the InAppBrowser to the main application.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_9

LANGUAGE: javascript
CODE:
```
window.addEventListener("messageFromWebview", (event) => {
  console.log(event);
});
```

----------------------------------------

TITLE: Set Webview URL
DESCRIPTION: Updates the URL displayed in the current InAppBrowser webview.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_18

LANGUAGE: APIDOC
CODE:
```
setUrl(options: { url: string })
  - Sets the URL of the active webview.
  - Parameters:
    - options: An object containing the `url` property (string) to navigate to.
  - Returns: A Promise that resolves with any value.
```

----------------------------------------

TITLE: InAppBrowser Title Visibility
DESCRIPTION: Controls whether the website's title is displayed in the InAppBrowser. If set to true, the title will be shown; otherwise, the title area will be empty.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_29

LANGUAGE: APIDOC
CODE:
```
visibleTitle: boolean
  Description: If true, the website title will be shown. If false, the title area will be shown empty.
  Default: true
  Version: 1.2.5
```

----------------------------------------

TITLE: Safe Bottom Margin Configuration (InAppBrowser)
DESCRIPTION: Enables or disables a 20px safe margin at the bottom of the WebView. When enabled, the WebView does not occupy the full height, creating a safe area outside the browser's view.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_35

LANGUAGE: typescript
CODE:
```
enabledSafeBottomMargin: boolean;
```

LANGUAGE: json
CODE:
```
{
  "enabledSafeBottomMargin": false
}
```

----------------------------------------

TITLE: Receive Event from Native in InAppBrowser
DESCRIPTION: Listens for events sent from the native application to the InAppBrowser.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_7

LANGUAGE: javascript
CODE:
```
window.addEventListener("messageFromNative", (event) => {
  console.log(event);
});
```

----------------------------------------

TITLE: HTTP Cookie Structure
DESCRIPTION: Represents an HTTP cookie with its associated URL, key, and value. Used for managing cookies within the InAppBrowser.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_23

LANGUAGE: APIDOC
CODE:
```
HttpCookie:
  url: string (The URL of the cookie.)
  key: string (The key of the cookie.)
  value: string (The value of the cookie.)
```

----------------------------------------

TITLE: Cookie Management
DESCRIPTION: Provides methods to clear specific cookies by URL or clear all cookies stored by the InAppBrowser.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_12

LANGUAGE: APIDOC
CODE:
```
clearCookies(options: ClearCookieOptions)
  - Clears cookies for a specified URL.
  - Parameters:
    - options: An object of type ClearCookieOptions, which must include the URL.
  - Returns: A Promise that resolves with any value.
  - Since: 0.5.0

clearAllCookies()
  - Clears all cookies stored by the InAppBrowser.
  - Returns: A Promise that resolves with any value.
  - Since: 6.5.0
```

----------------------------------------

TITLE: Close InAppBrowser from InAppBrowser
DESCRIPTION: Provides a method to close the InAppBrowser from within itself.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_10

LANGUAGE: javascript
CODE:
```
window.mobileApp.close();
```

----------------------------------------

TITLE: Clear Cookie Options
DESCRIPTION: Specifies the options for clearing cookies within the InAppBrowser, primarily requiring the URL from which to clear cookies.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_22

LANGUAGE: APIDOC
CODE:
```
ClearCookieOptions:
  url: string
```

----------------------------------------

TITLE: Prevent Deeplink Configuration (InAppBrowser)
DESCRIPTION: Controls whether deep links clicked within the InAppBrowser should be opened. If set to true, deep links are prevented from opening. For iOS, specific schemas must be added to `Info.plist` under `LSApplicationQueriesSchemes` for this to function correctly when set to false.

SOURCE: https://github.com/cap-go/capacitor-inappbrowser/blob/main/README.md#_snippet_33

LANGUAGE: typescript
CODE:
```
preventDeeplink: boolean;
```

LANGUAGE: json
CODE:
```
{
  "preventDeeplink": false
}
```