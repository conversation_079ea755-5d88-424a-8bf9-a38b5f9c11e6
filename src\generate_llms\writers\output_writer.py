"""
Output generation functionality for creating optimized llms.txt files.
"""

import logging
import json
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
from abc import ABC, abstractmethod

from ..core.models import Parsed<PERSON>onte<PERSON>, ProcessingResult, ProcessingConfig
from dataclasses import dataclass


@dataclass
class OutputMetrics:
    """Metrics for output generation."""

    estimated_tokens: int = 0
    estimated_reading_time_minutes: int = 0
    total_files: int = 0
    total_snippets: int = 0
    total_characters: int = 0
    context_window_warnings: List[str] = None

    def __post_init__(self):
        if self.context_window_warnings is None:
            self.context_window_warnings = []


class OutputWriter(ABC):
    """Abstract base class for output writers."""
    
    @abstractmethod
    def write(
        self, 
        contents: List[ParsedContent], 
        result: ProcessingResult, 
        config: ProcessingConfig
    ) -> None:
        """
        Write processed content to output file.
        
        Args:
            contents: List of parsed content with extracted snippets
            result: Processing result with metadata
            config: Processing configuration
        """
        pass


class MarkdownOutputWriter(OutputWriter):
    """
    Writer that generates token-efficient Markdown output optimized for LLMs.
    """
    
    def __init__(self):
        """Initialize the writer."""
        self.logger = logging.getLogger(__name__)
    
    def write(
        self,
        contents: List[ParsedContent],
        result: ProcessingResult,
        config: ProcessingConfig
    ) -> OutputMetrics:
        """
        Write contents to Markdown file optimized for LLM consumption.

        Args:
            contents: Parsed contents with code snippets
            result: Processing result with statistics
            config: Processing configuration

        Returns:
            OutputMetrics with generation statistics
        """
        self.logger.info(f"Writing output to {config.output_file}")

        # Calculate metrics before writing
        metrics = self._calculate_output_metrics(contents, config)

        # Check for context window warnings
        self._check_context_window_limits(metrics, config)

        try:
            with open(config.output_file, 'w', encoding='utf-8') as f:
                # Write header with token efficiency info
                self._write_header(f, config, metrics)

                # Write content sections
                self._write_content_sections(f, contents, config)

                # Write metadata
                self._write_metadata(f, result, config)

            self.logger.info(f"Successfully wrote output to {config.output_file}")
            self.logger.info(f"Output metrics: {metrics.estimated_tokens} tokens, {metrics.total_snippets} snippets")

            return metrics

        except Exception as e:
            error_msg = f"Failed to write output: {e}"
            self.logger.error(error_msg)
            raise
    
    def _write_header(self, file, config: ProcessingConfig, metrics: OutputMetrics) -> None:
        """Write file header with project information and token efficiency info."""
        file.write("# Project Documentation Summary\n\n")
        file.write(f"Generated from: `{config.input_directory}`\n")
        file.write(f"Extraction mode: {config.mode}\n")

        if config.languages:
            file.write(f"Languages: {', '.join(config.languages)}\n")

        # Add token efficiency information
        file.write(f"Estimated tokens: ~{metrics.estimated_tokens:,}\n")
        file.write(f"Estimated reading time: {metrics.estimated_reading_time_minutes} minutes\n")
        file.write(f"Total files: {metrics.total_files}, Total snippets: {metrics.total_snippets}\n")

        # Add context window warnings if any
        if metrics.context_window_warnings:
            file.write("\n**⚠️ Context Window Warnings:**\n")
            for warning in metrics.context_window_warnings:
                file.write(f"- {warning}\n")

        file.write("\n---\n\n")
    
    def _write_content_sections(
        self, 
        file, 
        contents: List[ParsedContent], 
        config: ProcessingConfig
    ) -> None:
        """Write content sections with code snippets."""
        
        for content in contents:
            if not content.snippets:
                continue
            
            # File header
            relative_path = self._get_relative_path(content.file_path, config.input_directory)
            file.write(f"## File: {relative_path}\n\n")
            
            # Write snippets for this file
            for snippet in content.snippets:
                self._write_snippet(file, snippet, config)
            
            file.write("\n")
    
    def _write_snippet(self, file, snippet, config: ProcessingConfig) -> None:
        """Write individual code snippet with context."""
        
        # Write heading if available
        if snippet.heading:
            file.write(f"### {snippet.heading}\n\n")
        
        # Write context before (full context mode only)
        if config.mode == "full_context" and snippet.context_before:
            file.write(f"{snippet.context_before}\n\n")
        
        # Write code block
        language = snippet.language or ""
        file.write(f"```{language}\n")
        file.write(snippet.content)
        file.write("\n```\n\n")
        
        # Write context after (full context mode only)
        if config.mode == "full_context" and snippet.context_after:
            file.write(f"{snippet.context_after}\n\n")
    
    def _write_metadata(self, file, result: ProcessingResult, config: ProcessingConfig) -> None:
        """Write comprehensive metadata as JSON."""
        file.write("---\n\n")
        file.write("## Processing Metadata\n\n")
        
        # Update result metadata with config info
        metadata = result.to_metadata_dict()
        metadata["generation"]["processing_mode"] = config.mode
        metadata["generation"]["llm_rewrite_enabled"] = config.llm_rewrite_enabled
        metadata["source"]["root_path"] = str(config.input_directory.absolute())
        
        # Write formatted JSON
        file.write("```json\n")
        json.dump(metadata, file, indent=2, ensure_ascii=False)
        file.write("\n```\n")
    
    def _get_relative_path(self, file_path: Path, root_path: Path) -> str:
        """Get relative path for display."""
        try:
            return str(file_path.relative_to(root_path))
        except ValueError:
            return str(file_path)
    
    def estimate_tokens(self, contents: List[ParsedContent]) -> int:
        """
        Estimate token count for the output.
        
        Args:
            contents: Parsed contents to estimate
            
        Returns:
            Estimated token count
        """
        total_chars = 0
        
        for content in contents:
            for snippet in content.snippets:
                # Count code content
                total_chars += len(snippet.content)
                
                # Count context
                if snippet.context_before:
                    total_chars += len(snippet.context_before)
                if snippet.context_after:
                    total_chars += len(snippet.context_after)
                if snippet.heading:
                    total_chars += len(snippet.heading)
        
        # Rough estimation: 4 characters per token on average
        return total_chars // 4
    
    def estimate_reading_time(self, contents: List[ParsedContent]) -> int:
        """
        Estimate reading time in minutes.
        
        Args:
            contents: Parsed contents to estimate
            
        Returns:
            Estimated reading time in minutes
        """
        total_words = 0
        
        for content in contents:
            for snippet in content.snippets:
                # Count words in context (code is typically read slower)
                if snippet.context_before:
                    total_words += len(snippet.context_before.split())
                if snippet.context_after:
                    total_words += len(snippet.context_after.split())
                if snippet.heading:
                    total_words += len(snippet.heading.split())
                
                # Code is read at ~50% speed of normal text
                code_words = len(snippet.content.split())
                total_words += code_words * 0.5
        
        # Average reading speed: 200 words per minute
        return max(1, int(total_words / 200))

    def _calculate_output_metrics(self, contents: List[ParsedContent], config: ProcessingConfig) -> OutputMetrics:
        """Calculate comprehensive output metrics."""
        total_chars = 0
        total_snippets = 0
        files_with_snippets = 0

        for content in contents:
            if content.snippets:
                files_with_snippets += 1

            for snippet in content.snippets:
                total_snippets += 1

                # Count all content
                total_chars += len(snippet.content)
                if snippet.context_before:
                    total_chars += len(snippet.context_before)
                if snippet.context_after:
                    total_chars += len(snippet.context_after)
                if snippet.heading:
                    total_chars += len(snippet.heading)

        # Add overhead for markdown formatting (headers, code blocks, etc.)
        formatting_overhead = total_snippets * 50  # ~50 chars per snippet for formatting
        total_chars += formatting_overhead

        return OutputMetrics(
            estimated_tokens=total_chars // 4,  # 4 chars per token average
            estimated_reading_time_minutes=self.estimate_reading_time(contents),
            total_files=len(contents),
            total_snippets=total_snippets,
            total_characters=total_chars
        )

    def _check_context_window_limits(self, metrics: OutputMetrics, config: ProcessingConfig) -> None:
        """Check for context window limit warnings."""
        warnings = []

        # Common LLM context window limits
        context_limits = {
            "GPT-4": 128000,
            "GPT-3.5": 16000,
            "Claude-3": 200000,
            "Claude-2": 100000,
            "Llama-2": 4000,
            "Gemini Pro": 32000
        }

        for model, limit in context_limits.items():
            if metrics.estimated_tokens > limit:
                warnings.append(f"Output ({metrics.estimated_tokens:,} tokens) exceeds {model} context window ({limit:,} tokens)")

        # General warnings
        if metrics.estimated_tokens > 100000:
            warnings.append("Very large output may cause performance issues with some LLMs")
        elif metrics.estimated_tokens > 50000:
            warnings.append("Large output - consider using snippets-only mode for better performance")

        metrics.context_window_warnings = warnings


class MetadataGenerator:
    """
    Generator for comprehensive processing metadata and quality metrics.
    """

    def __init__(self):
        """Initialize the generator."""
        self.logger = logging.getLogger(__name__)

        # JSON schema for metadata validation
        self.metadata_schema = {
            "type": "object",
            "properties": {
                "generation": {
                    "type": "object",
                    "properties": {
                        "timestamp": {"type": "string"},
                        "tool_version": {"type": "string"},
                        "processing_mode": {"type": "string"},
                        "llm_rewrite_enabled": {"type": "boolean"}
                    },
                    "required": ["timestamp", "tool_version", "processing_mode", "llm_rewrite_enabled"]
                },
                "source": {
                    "type": "object",
                    "properties": {
                        "root_path": {"type": "string"},
                        "total_files_scanned": {"type": "integer"},
                        "files_with_snippets": {"type": "integer"},
                        "excluded_files": {"type": "integer"}
                    },
                    "required": ["root_path", "total_files_scanned", "files_with_snippets", "excluded_files"]
                },
                "extraction": {
                    "type": "object",
                    "properties": {
                        "total_snippets": {"type": "integer"},
                        "languages_detected": {"type": "object"},
                        "scan_depth": {"type": ["integer", "null"]}
                    },
                    "required": ["total_snippets", "languages_detected"]
                },
                "quality": {
                    "type": "object",
                    "properties": {
                        "trust_score": {"type": "number", "minimum": 0, "maximum": 10},
                        "completeness_score": {"type": "number", "minimum": 0, "maximum": 10},
                        "code_coverage_percentage": {"type": "number", "minimum": 0, "maximum": 100}
                    },
                    "required": ["trust_score", "completeness_score", "code_coverage_percentage"]
                },
                "output": {
                    "type": "object",
                    "properties": {
                        "estimated_tokens": {"type": "integer"},
                        "estimated_reading_time_minutes": {"type": "integer"},
                        "processing_time_seconds": {"type": "number"}
                    },
                    "required": ["estimated_tokens", "estimated_reading_time_minutes", "processing_time_seconds"]
                }
            },
            "required": ["generation", "source", "extraction", "quality", "output"]
        }
    
    def calculate_metrics(
        self, 
        contents: List[ParsedContent], 
        result: ProcessingResult,
        config: ProcessingConfig
    ) -> None:
        """
        Calculate and update quality metrics in the result.
        
        Args:
            contents: Processed contents
            result: Result object to update
            config: Processing configuration
        """
        start_time = time.time()
        self.logger.debug("Calculating comprehensive quality metrics")

        try:
            # Calculate trust score
            result.trust_score = self._calculate_trust_score(contents, result)

            # Calculate completeness score
            result.completeness_score = self._calculate_completeness_score(contents)

            # Calculate code coverage percentage
            result.code_coverage_percentage = self._calculate_code_coverage(contents, result)

            # Estimate tokens and reading time
            writer = MarkdownOutputWriter()
            result.estimated_tokens = writer.estimate_tokens(contents)
            result.estimated_reading_time_minutes = writer.estimate_reading_time(contents)

            # Calculate language distribution
            language_stats = self._calculate_language_distribution(contents)

            # Calculate processing statistics
            processing_time = time.time() - start_time

            # Generate comprehensive metadata
            metadata = self._generate_comprehensive_metadata(contents, result, config, processing_time, language_stats)

            # Validate metadata against schema
            if self._validate_metadata_schema(metadata):
                result.metadata = metadata
                self.logger.debug("Metadata schema validation passed")
            else:
                self.logger.warning("Metadata schema validation failed")

            self.logger.debug(f"Quality metrics calculated in {processing_time:.3f}s: trust={result.trust_score:.1f}, completeness={result.completeness_score:.1f}, coverage={result.code_coverage_percentage:.1f}%")

        except Exception as e:
            self.logger.error(f"Error calculating metrics: {e}")
            result.errors.append(f"Metrics calculation error: {e}")
    
    def _calculate_trust_score(self, contents: List[ParsedContent], result: ProcessingResult) -> float:
        """Calculate trust score based on processing quality (0-10 scale)."""
        score = 10.0
        
        # Penalize for errors
        if result.errors:
            score -= min(3.0, len(result.errors) * 0.5)
        
        # Penalize for files that couldn't be parsed
        total_files = result.total_files_scanned
        if total_files > 0:
            parse_success_rate = (total_files - len(result.errors)) / total_files
            score *= parse_success_rate
        
        # Bonus for good language detection
        detected_languages = sum(1 for content in contents 
                                for snippet in content.snippets 
                                if snippet.language and snippet.language != "unknown")
        total_snippets = sum(len(content.snippets) for content in contents)
        
        if total_snippets > 0:
            detection_rate = detected_languages / total_snippets
            score += detection_rate * 1.0  # Up to 1 point bonus
        
        return max(0.0, min(10.0, score))
    
    def _calculate_completeness_score(self, contents: List[ParsedContent]) -> float:
        """Calculate completeness score based on documentation coverage (0-10 scale)."""
        total_snippets = 0
        documented_snippets = 0
        
        for content in contents:
            for snippet in content.snippets:
                total_snippets += 1
                if snippet.context_before or snippet.context_after or snippet.heading:
                    documented_snippets += 1
        
        if total_snippets == 0:
            return 0.0
        
        return (documented_snippets / total_snippets) * 10.0
    
    def _calculate_code_coverage(self, contents: List[ParsedContent], result: ProcessingResult) -> float:
        """Calculate code coverage percentage."""
        files_with_code = result.files_with_snippets
        total_files = result.total_files_scanned
        
        if total_files == 0:
            return 0.0
        
        return (files_with_code / total_files) * 100.0

    def _calculate_language_distribution(self, contents: List[ParsedContent]) -> Dict[str, int]:
        """Calculate distribution of programming languages."""
        language_counts = {}

        for content in contents:
            for snippet in content.snippets:
                if snippet.language:
                    language_counts[snippet.language] = language_counts.get(snippet.language, 0) + 1

        return language_counts

    def _generate_comprehensive_metadata(
        self,
        contents: List[ParsedContent],
        result: ProcessingResult,
        config: ProcessingConfig,
        processing_time: float,
        language_stats: Dict[str, int]
    ) -> Dict[str, Any]:
        """Generate comprehensive metadata following JSON schema."""
        from datetime import datetime

        return {
            "generation": {
                "timestamp": datetime.now().isoformat(),
                "tool_version": "1.0.0",
                "processing_mode": config.mode,
                "llm_rewrite_enabled": bool(config.llm_backend or config.llm_rewrite_enabled)
            },
            "source": {
                "root_path": str(config.input_directory),
                "total_files_scanned": result.total_files_scanned,
                "files_with_snippets": result.files_with_snippets,
                "excluded_files": result.excluded_files
            },
            "extraction": {
                "total_snippets": result.total_snippets,
                "languages_detected": language_stats,
                "scan_depth": result.scan_depth
            },
            "quality": {
                "trust_score": result.trust_score,
                "completeness_score": result.completeness_score,
                "code_coverage_percentage": result.code_coverage_percentage
            },
            "output": {
                "estimated_tokens": result.estimated_tokens,
                "estimated_reading_time_minutes": result.estimated_reading_time_minutes,
                "processing_time_seconds": processing_time
            }
        }

    def _validate_metadata_schema(self, metadata: Dict[str, Any]) -> bool:
        """Validate metadata against JSON schema."""
        try:
            # Simple validation - check required fields exist
            required_sections = ["generation", "source", "extraction", "quality", "output"]

            for section in required_sections:
                if section not in metadata:
                    self.logger.error(f"Missing required metadata section: {section}")
                    return False

            # Validate specific field types and ranges
            quality = metadata["quality"]
            if not (0 <= quality["trust_score"] <= 10):
                self.logger.error(f"Trust score out of range: {quality['trust_score']}")
                return False

            if not (0 <= quality["completeness_score"] <= 10):
                self.logger.error(f"Completeness score out of range: {quality['completeness_score']}")
                return False

            if not (0 <= quality["code_coverage_percentage"] <= 100):
                self.logger.error(f"Code coverage out of range: {quality['code_coverage_percentage']}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"Metadata validation error: {e}")
            return False
