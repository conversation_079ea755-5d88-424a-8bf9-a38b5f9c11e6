"""
Context extraction functionality for preserving meaningful context around code snippets.
"""

import logging
import re
from typing import List, Optional, Dict, Tuple
from dataclasses import dataclass

from ..core.models import CodeSnippet, ParsedContent, ProcessingConfig


@dataclass
class ContextInfo:
    """Information about context around a code snippet."""
    
    heading: Optional[str] = None
    heading_level: int = 0
    context_before: Optional[str] = None
    context_after: Optional[str] = None
    word_count_before: int = 0
    word_count_after: int = 0


class ContextExtractor:
    """
    Extracts meaningful context around code snippets for better LLM understanding.
    """
    
    def __init__(self):
        """Initialize the context extractor."""
        self.logger = logging.getLogger(__name__)
        
        # Regex patterns for different heading styles
        self.markdown_heading_pattern = re.compile(r'^(#{1,6})\s+(.+)$', re.MULTILINE)
        self.rst_heading_pattern = re.compile(r'^(.+)\n([=\-~`#*+^"\']{3,})\s*$', re.MULTILINE)
        self.html_heading_pattern = re.compile(r'<h([1-6])[^>]*>(.+?)</h[1-6]>', re.IGNORECASE)
        
        # Pattern for paragraph boundaries
        self.paragraph_boundary = re.compile(r'\n\s*\n')
        
        # Maximum context word limits
        self.max_context_words = 200
        self.max_context_chars = 1000
    
    def add_context_to_snippets(
        self, 
        content: ParsedContent, 
        config: ProcessingConfig
    ) -> None:
        """
        Add context information to code snippets based on processing mode.
        
        Args:
            content: Parsed content with extracted snippets
            config: Processing configuration with mode settings
        """
        if not content.snippets or not content.raw_content:
            return
        
        self.logger.debug(f"Adding context to {len(content.snippets)} snippets from {content.file_path}")
        
        # Skip context extraction for snippets-only mode
        if config.mode == "snippets_only":
            self.logger.debug("Snippets-only mode: skipping context extraction")
            return
        
        # Extract context based on file type
        if content.file_type == "markdown":
            self._add_markdown_context(content, config)
        elif content.file_type == "html":
            self._add_html_context(content, config)
        elif content.file_type == "rst":
            self._add_rst_context(content, config)
        elif content.file_type in ["jupyter", "asciidoc"]:
            # These are converted to markdown-like format, so use markdown context
            self._add_markdown_context(content, config)
        else:
            # Fallback to generic context extraction
            self._add_generic_context(content, config)
        
        self.logger.debug(f"Context added to snippets from {content.file_path}")
    
    def _add_markdown_context(self, content: ParsedContent, config: ProcessingConfig) -> None:
        """Add context for Markdown files using heading hierarchy."""
        text = content.raw_content
        lines = text.split('\n')
        
        # Build heading hierarchy
        headings = self._extract_markdown_headings(text)
        
        for snippet in content.snippets:
            if not snippet.start_line:
                continue
            
            context_info = self._find_context_around_line(
                lines, snippet.start_line, snippet.end_line, headings
            )
            
            # Apply context to snippet
            snippet.heading = context_info.heading
            snippet.context_before = context_info.context_before
            snippet.context_after = context_info.context_after
    
    def _add_html_context(self, content: ParsedContent, config: ProcessingConfig) -> None:
        """Add context for HTML files using heading tags."""
        text = content.raw_content
        
        # Extract HTML headings
        headings = []
        for match in self.html_heading_pattern.finditer(text):
            level = int(match.group(1))
            title = self._clean_html_content(match.group(2))
            line_num = text[:match.start()].count('\n') + 1
            headings.append((line_num, level, title))
        
        lines = text.split('\n')
        
        for snippet in content.snippets:
            if not snippet.start_line:
                continue
            
            context_info = self._find_context_around_line(
                lines, snippet.start_line, snippet.end_line, headings
            )
            
            snippet.heading = context_info.heading
            snippet.context_before = context_info.context_before
            snippet.context_after = context_info.context_after
    
    def _add_rst_context(self, content: ParsedContent, config: ProcessingConfig) -> None:
        """Add context for RST files using RST heading syntax."""
        text = content.raw_content
        lines = text.split('\n')
        
        # Extract RST headings
        headings = self._extract_rst_headings(text)
        
        for snippet in content.snippets:
            if not snippet.start_line:
                continue
            
            context_info = self._find_context_around_line(
                lines, snippet.start_line, snippet.end_line, headings
            )
            
            snippet.heading = context_info.heading
            snippet.context_before = context_info.context_before
            snippet.context_after = context_info.context_after
    
    def _add_generic_context(self, content: ParsedContent, config: ProcessingConfig) -> None:
        """Add generic context for unknown file types."""
        text = content.raw_content
        lines = text.split('\n')
        
        for snippet in content.snippets:
            if not snippet.start_line:
                continue
            
            # Simple context extraction without heading detection
            context_info = self._find_context_around_line(
                lines, snippet.start_line, snippet.end_line, []
            )
            
            snippet.context_before = context_info.context_before
            snippet.context_after = context_info.context_after
    
    def _extract_markdown_headings(self, text: str) -> List[Tuple[int, int, str]]:
        """
        Extract markdown headings with line numbers and levels.
        
        Returns:
            List of (line_number, level, title) tuples
        """
        headings = []
        
        for match in self.markdown_heading_pattern.finditer(text):
            level = len(match.group(1))  # Number of # characters
            title = match.group(2).strip()
            line_num = text[:match.start()].count('\n') + 1
            headings.append((line_num, level, title))
        
        return headings
    
    def _extract_rst_headings(self, text: str) -> List[Tuple[int, int, str]]:
        """
        Extract RST headings with line numbers and levels.
        
        Returns:
            List of (line_number, level, title) tuples
        """
        headings = []
        
        # RST heading levels are determined by the underline character
        level_chars = ['=', '-', '~', '`', '#', '*', '+', '^', '"', "'"]
        char_to_level = {}
        
        for match in self.rst_heading_pattern.finditer(text):
            title = match.group(1).strip()
            underline_char = match.group(2)[0]
            line_num = text[:match.start()].count('\n') + 1
            
            # Assign level based on first occurrence order
            if underline_char not in char_to_level:
                char_to_level[underline_char] = len(char_to_level) + 1
            
            level = char_to_level[underline_char]
            headings.append((line_num, level, title))
        
        return headings

    def _find_context_around_line(
        self,
        lines: List[str],
        start_line: int,
        end_line: Optional[int],
        headings: List[Tuple[int, int, str]]
    ) -> ContextInfo:
        """
        Find context around a specific line range.

        Args:
            lines: All lines in the document
            start_line: Starting line number (1-based)
            end_line: Ending line number (1-based), optional
            headings: List of (line_number, level, title) tuples

        Returns:
            ContextInfo with extracted context
        """
        context_info = ContextInfo()

        # Find the most relevant heading
        context_info.heading = self._find_relevant_heading(start_line, headings)

        # Extract context before the code block
        context_before_lines = self._extract_context_before(
            lines, start_line, headings
        )
        if context_before_lines:
            context_info.context_before = '\n'.join(context_before_lines)
            context_info.word_count_before = len(context_info.context_before.split())

        # Extract context after the code block
        end_line_num = end_line or start_line
        context_after_lines = self._extract_context_after(
            lines, end_line_num
        )
        if context_after_lines:
            context_info.context_after = '\n'.join(context_after_lines)
            context_info.word_count_after = len(context_info.context_after.split())

        return context_info

    def _find_relevant_heading(
        self,
        line_number: int,
        headings: List[Tuple[int, int, str]]
    ) -> Optional[str]:
        """
        Find the most relevant heading for a given line number.

        Args:
            line_number: Line number to find heading for
            headings: List of (line_number, level, title) tuples

        Returns:
            Most relevant heading title or None
        """
        if not headings:
            return None

        # Find the last heading before the line number
        relevant_heading = None
        min_level = float('inf')

        for heading_line, level, title in headings:
            if heading_line < line_number:
                # Prefer lower level (more specific) headings that are closer
                if level <= min_level:
                    relevant_heading = title
                    min_level = level
            else:
                break

        return relevant_heading

    def _extract_context_before(
        self,
        lines: List[str],
        start_line: int,
        headings: List[Tuple[int, int, str]]
    ) -> List[str]:
        """
        Extract context lines before a code block.

        Args:
            lines: All document lines
            start_line: Starting line of code block (1-based)
            headings: List of headings to avoid including

        Returns:
            List of context lines
        """
        if start_line <= 1:
            return []

        # Convert to 0-based indexing
        start_idx = start_line - 1

        # Look backwards for context, but stop at headings or empty paragraphs
        context_lines = []
        word_count = 0

        for i in range(start_idx - 1, -1, -1):
            line = lines[i].strip()

            # Stop at headings
            if self._is_heading_line(i + 1, headings):
                break

            # Stop at paragraph boundaries (empty lines)
            if not line and context_lines:
                break

            # Skip empty lines at the beginning
            if not line and not context_lines:
                continue

            # Check word count limit
            line_words = len(line.split())
            if word_count + line_words > self.max_context_words:
                break

            context_lines.insert(0, lines[i])
            word_count += line_words

            # Stop if we have enough context
            if word_count >= 50:  # Minimum meaningful context
                break

        # Clean up context lines
        return self._clean_context_lines(context_lines)

    def _extract_context_after(
        self,
        lines: List[str],
        end_line: int
    ) -> List[str]:
        """
        Extract context lines after a code block.

        Args:
            lines: All document lines
            end_line: Ending line of code block (1-based)

        Returns:
            List of context lines
        """
        if end_line >= len(lines):
            return []

        # Convert to 0-based indexing
        end_idx = end_line - 1

        # Look forward for context
        context_lines = []
        word_count = 0

        for i in range(end_idx + 1, len(lines)):
            line = lines[i].strip()

            # Stop at headings (simple check for markdown)
            if line.startswith('#') or line.startswith('=') or line.startswith('-'):
                break

            # Stop at paragraph boundaries after collecting some content
            if not line and context_lines:
                break

            # Skip empty lines at the beginning
            if not line and not context_lines:
                continue

            # Check word count limit
            line_words = len(line.split())
            if word_count + line_words > self.max_context_words:
                break

            context_lines.append(lines[i])
            word_count += line_words

            # Stop if we have enough context
            if word_count >= 50:  # Minimum meaningful context
                break

        # Clean up context lines
        return self._clean_context_lines(context_lines)

    def _is_heading_line(self, line_number: int, headings: List[Tuple[int, int, str]]) -> bool:
        """Check if a line number corresponds to a heading."""
        return any(heading_line == line_number for heading_line, _, _ in headings)

    def _clean_context_lines(self, lines: List[str]) -> List[str]:
        """Clean and format context lines."""
        if not lines:
            return []

        # Remove leading/trailing empty lines
        while lines and not lines[0].strip():
            lines.pop(0)
        while lines and not lines[-1].strip():
            lines.pop()

        return lines

    def _clean_html_content(self, content: str) -> str:
        """Clean HTML entities and tags from content."""
        # Basic HTML entity decoding
        content = content.replace('&lt;', '<')
        content = content.replace('&gt;', '>')
        content = content.replace('&amp;', '&')
        content = content.replace('&quot;', '"')
        content = content.replace('&#39;', "'")

        # Remove HTML tags
        content = re.sub(r'<[^>]+>', '', content)

        return content.strip()
