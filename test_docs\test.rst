Test RST Document
==================

This is a reStructuredText document with code examples.

Python Example
--------------

Here's a Python code block:

.. code-block:: python

    def hello_world():
        """Print hello world."""
        print("Hello, World!")
        return True

Literal Block
-------------

This is a literal block::

    $ pip install generate-llms
    $ generate-llms --help

Another code example:

.. code:: bash

    echo "Hello from bash"
    ls -la
