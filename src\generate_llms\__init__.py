"""
Generate LLMs - Local Documentation Code Snippet Aggregator

A CLI tool that recursively scans local directories for documentation files,
extracts code snippets with contextual information, and generates a consolidated
llms.txt file optimized for LLM consumption.
"""

__version__ = "1.0.0"
__author__ = "The Augster"
__email__ = "<EMAIL>"

from .core.models import (
    CodeSnippet,
    ParsedContent,
    ProcessingConfig,
    ProcessingResult,
)

__all__ = [
    "__version__",
    "__author__",
    "__email__",
    "CodeSnippet",
    "ParsedContent", 
    "ProcessingConfig",
    "ProcessingResult",
]
